{% extends 'base.html' %}

{% block title %}Book Tickets - {{ showtime.movie.title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4>Select Seats</h4>
            </div>
            <div class="card-body">
                <!-- Movie Info -->
                <div class="mb-4">
                    <h5>{{ showtime.movie.title }}</h5>
                    <p class="text-muted">
                        {{ showtime.screen.theatre.name }} - {{ showtime.screen.name }}<br>
                        {{ showtime.date|date:"l, F d, Y" }} at {{ showtime.time|time:"g:i A" }}
                    </p>
                </div>
                
                <!-- Screen -->
                <div class="screen">
                    <i class="fas fa-desktop me-2"></i>SCREEN
                </div>
                
                <!-- Seat Layout -->
                <div class="seat-layout">
                    <form method="post" action="{% url 'bookings:confirm_booking' %}" id="booking-form">
                        {% csrf_token %}
                        <input type="hidden" name="showtime_id" value="{{ showtime.pk }}">
                        
                        {% for row in seat_layout %}
                        <div class="text-center mb-2">
                            {% for seat in row %}
                            <span class="seat {% if seat in booked_seats %}booked{% endif %}" 
                                  data-seat="{{ seat }}" 
                                  {% if seat not in booked_seats %}onclick="toggleSeat(this)"{% endif %}>
                                {{ seat }}
                            </span>
                            {% endfor %}
                        </div>
                        {% endfor %}
                        
                        <!-- Legend -->
                        <div class="mt-4 text-center">
                            <span class="seat me-3">Available</span>
                            <span class="seat booked me-3">Booked</span>
                            <span class="seat selected">Selected</span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="booking-summary">
            <h5>Booking Summary</h5>
            <hr>
            
            <div class="mb-3">
                <strong>Movie:</strong> {{ showtime.movie.title }}<br>
                <strong>Theatre:</strong> {{ showtime.screen.theatre.name }}<br>
                <strong>Screen:</strong> {{ showtime.screen.name }}<br>
                <strong>Date:</strong> {{ showtime.date|date:"M d, Y" }}<br>
                <strong>Time:</strong> {{ showtime.time|time:"g:i A" }}
            </div>
            
            <div class="mb-3">
                <strong>Selected Seats:</strong>
                <div id="selected-seats">None</div>
            </div>
            
            <div class="mb-3">
                <strong>Price per ticket:</strong> ₹{{ showtime.price }}
            </div>
            
            <div class="mb-3">
                <strong>Total Amount:</strong> ₹<span id="total-amount">0</span>
            </div>
            
            <button type="submit" form="booking-form" class="btn btn-primary w-100" id="confirm-btn" disabled>
                Confirm Booking
            </button>
        </div>
    </div>
</div>

<script>
let selectedSeats = [];
const pricePerTicket = {{ showtime.price }};

function toggleSeat(seatElement) {
    const seatNumber = seatElement.getAttribute('data-seat');
    
    if (seatElement.classList.contains('selected')) {
        // Deselect seat
        seatElement.classList.remove('selected');
        selectedSeats = selectedSeats.filter(seat => seat !== seatNumber);
        
        // Remove hidden input
        const input = document.querySelector(`input[name="seats"][value="${seatNumber}"]`);
        if (input) input.remove();
    } else {
        // Select seat
        seatElement.classList.add('selected');
        selectedSeats.push(seatNumber);
        
        // Add hidden input
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'seats';
        input.value = seatNumber;
        document.getElementById('booking-form').appendChild(input);
    }
    
    updateSummary();
}

function updateSummary() {
    const selectedSeatsDiv = document.getElementById('selected-seats');
    const totalAmountSpan = document.getElementById('total-amount');
    const confirmBtn = document.getElementById('confirm-btn');
    
    if (selectedSeats.length > 0) {
        selectedSeatsDiv.textContent = selectedSeats.join(', ');
        totalAmountSpan.textContent = (selectedSeats.length * pricePerTicket).toFixed(2);
        confirmBtn.disabled = false;
    } else {
        selectedSeatsDiv.textContent = 'None';
        totalAmountSpan.textContent = '0';
        confirmBtn.disabled = true;
    }
}
</script>
{% endblock %}
