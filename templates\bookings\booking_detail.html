{% extends 'base.html' %}

{% block title %}Booking Details - {{ booking.booking_id }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4>Booking Details</h4>
                <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% else %}danger{% endif %} fs-6">
                    {{ booking.get_status_display }}
                </span>
            </div>
            <div class="card-body">
                <!-- Booking ID -->
                <div class="row mb-4">
                    <div class="col-12 text-center">
                        <h5>Booking ID: {{ booking.booking_id }}</h5>
                        <p class="text-muted">{{ booking.booking_date|date:"F d, Y g:i A" }}</p>
                    </div>
                </div>
                
                <!-- Movie Details -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>Movie Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Movie:</strong></td>
                                <td>{{ booking.showtime.movie.title }}</td>
                            </tr>
                            <tr>
                                <td><strong>Genre:</strong></td>
                                <td>{{ booking.showtime.movie.get_genre_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>Language:</strong></td>
                                <td>{{ booking.showtime.movie.get_language_display }}</td>
                            </tr>
                            <tr>
                                <td><strong>Duration:</strong></td>
                                <td>{{ booking.showtime.movie.duration }} minutes</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Show Details</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Theatre:</strong></td>
                                <td>{{ booking.showtime.screen.theatre.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Screen:</strong></td>
                                <td>{{ booking.showtime.screen.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date:</strong></td>
                                <td>{{ booking.showtime.date|date:"l, F d, Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>Time:</strong></td>
                                <td>{{ booking.showtime.time|time:"g:i A" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Booking Details -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>Booking Information</h6>
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Seats:</strong></td>
                                <td>
                                    {% for seat in booking.get_seats_list %}
                                    <span class="badge bg-primary me-1">{{ seat }}</span>
                                    {% endfor %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Number of Tickets:</strong></td>
                                <td>{{ booking.get_seats_list|length }}</td>
                            </tr>
                            <tr>
                                <td><strong>Price per Ticket:</strong></td>
                                <td>₹{{ booking.showtime.price }}</td>
                            </tr>
                            <tr>
                                <td><strong>Total Amount:</strong></td>
                                <td><strong class="text-success">₹{{ booking.total_amount }}</strong></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="text-center">
                    {% if booking.status == 'confirmed' %}
                    <a href="{% url 'bookings:cancel_booking' booking.booking_id %}" 
                       class="btn btn-danger">Cancel Booking</a>
                    {% endif %}
                    <a href="{% url 'bookings:my_bookings' %}" class="btn btn-secondary">Back to My Bookings</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
