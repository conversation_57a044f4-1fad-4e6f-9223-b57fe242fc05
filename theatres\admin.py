from django.contrib import admin
from .models import Theatre, Screen, ShowTime

@admin.register(Theatre)
class TheatreAdmin(admin.ModelAdmin):
    list_display = ['name', 'city', 'phone', 'is_active']
    list_filter = ['city', 'is_active']
    search_fields = ['name', 'city', 'address']
    list_editable = ['is_active']

@admin.register(Screen)
class ScreenAdmin(admin.ModelAdmin):
    list_display = ['name', 'theatre', 'total_seats', 'rows', 'seats_per_row', 'is_active']
    list_filter = ['theatre', 'is_active']
    search_fields = ['name', 'theatre__name']
    list_editable = ['is_active']

@admin.register(ShowTime)
class ShowTimeAdmin(admin.ModelAdmin):
    list_display = ['movie', 'screen', 'date', 'time', 'price', 'is_active']
    list_filter = ['date', 'screen__theatre', 'movie', 'is_active']
    search_fields = ['movie__title', 'screen__name', 'screen__theatre__name']
    list_editable = ['price', 'is_active']
    date_hierarchy = 'date'
    ordering = ['-date', 'time']
