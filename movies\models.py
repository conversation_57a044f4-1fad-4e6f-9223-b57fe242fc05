from django.db import models
from django.urls import reverse

class Movie(models.Model):
    GENRE_CHOICES = [
        ('action', 'Action'),
        ('comedy', 'Comedy'),
        ('drama', 'Drama'),
        ('horror', 'Horror'),
        ('romance', 'Romance'),
        ('thriller', 'Thriller'),
        ('sci-fi', 'Science Fiction'),
        ('fantasy', 'Fantasy'),
        ('animation', 'Animation'),
        ('documentary', 'Documentary'),
    ]

    LANGUAGE_CHOICES = [
        ('english', 'English'),
        ('hindi', 'Hindi'),
        ('tamil', 'Tamil'),
        ('telugu', 'Telugu'),
        ('malayalam', 'Malayalam'),
        ('kannada', 'Kannada'),
        ('bengali', 'Bengali'),
        ('marathi', 'Marathi'),
    ]

    title = models.CharField(max_length=200)
    description = models.TextField()
    genre = models.CharField(max_length=20, choices=GENRE_CHOICES)
    language = models.CharField(max_length=20, choices=LANGUAGE_CHOICES)
    poster = models.ImageField(upload_to='movie_posters/', blank=True, null=True)
    duration = models.PositiveIntegerField(help_text="Duration in minutes")
    release_date = models.DateField()
    cast = models.TextField(help_text="Comma-separated list of cast members")
    director = models.CharField(max_length=100)
    rating = models.DecimalField(max_digits=3, decimal_places=1, default=0.0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-release_date']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('movies:detail', kwargs={'pk': self.pk})

    def get_cast_list(self):
        return [cast.strip() for cast in self.cast.split(',') if cast.strip()]
