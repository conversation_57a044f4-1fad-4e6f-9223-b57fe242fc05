from django.db import models
from django.urls import reverse
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator

class Movie(models.Model):
    GENRE_CHOICES = [
        ('action', 'Action'),
        ('comedy', 'Comedy'),
        ('drama', 'Drama'),
        ('horror', 'Horror'),
        ('romance', 'Romance'),
        ('thriller', 'Thriller'),
        ('sci-fi', 'Science Fiction'),
        ('fantasy', 'Fantasy'),
        ('animation', 'Animation'),
        ('documentary', 'Documentary'),
    ]

    LANGUAGE_CHOICES = [
        ('english', 'English'),
        ('hindi', 'Hindi'),
        ('tamil', 'Tamil'),
        ('telugu', 'Telugu'),
        ('malayalam', 'Malayalam'),
        ('kannada', 'Kannada'),
        ('bengali', 'Bengali'),
        ('marathi', 'Marathi'),
    ]

    # Basic Information
    title = models.CharField(max_length=200)
    description = models.TextField()
    genre = models.CharField(max_length=20, choices=GENRE_CHOICES)
    language = models.CharField(max_length=20, choices=LANGUAGE_CHOICES)
    poster = models.ImageField(upload_to='movie_posters/', blank=True, null=True)
    duration = models.PositiveIntegerField(help_text="Duration in minutes")
    release_date = models.DateField()
    cast = models.TextField(help_text="Comma-separated list of cast members")
    director = models.CharField(max_length=100)
    rating = models.DecimalField(max_digits=3, decimal_places=1, default=0.0)

    # Enhanced Fields
    tmdb_id = models.IntegerField(null=True, blank=True, unique=True, help_text="TMDB Movie ID")
    imdb_id = models.CharField(max_length=20, blank=True, help_text="IMDB Movie ID")
    backdrop_path = models.URLField(blank=True, help_text="Backdrop image URL from TMDB")
    poster_path = models.URLField(blank=True, help_text="Poster image URL from TMDB")
    trailer_url = models.URLField(blank=True, help_text="YouTube trailer URL")
    budget = models.BigIntegerField(null=True, blank=True)
    revenue = models.BigIntegerField(null=True, blank=True)
    popularity = models.FloatField(default=0.0)
    vote_average = models.FloatField(default=0.0)
    vote_count = models.IntegerField(default=0)
    adult = models.BooleanField(default=False)
    original_title = models.CharField(max_length=200, blank=True)
    original_language = models.CharField(max_length=10, blank=True)
    tagline = models.CharField(max_length=500, blank=True)
    homepage = models.URLField(blank=True)

    # Status and Metadata
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    is_now_showing = models.BooleanField(default=True)
    is_coming_soon = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-release_date']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('movies:detail', kwargs={'pk': self.pk})

    def get_cast_list(self):
        return [cast.strip() for cast in self.cast.split(',') if cast.strip()]

    def get_poster_url(self):
        """Get poster URL, preferring TMDB over uploaded image"""
        if self.poster_path:
            return self.poster_path
        elif self.poster:
            return self.poster.url
        return None

    def get_backdrop_url(self):
        """Get backdrop URL from TMDB"""
        return self.backdrop_path if self.backdrop_path else None

    def get_average_rating(self):
        """Calculate average user rating"""
        reviews = self.reviews.all()
        if reviews:
            return sum(review.rating for review in reviews) / len(reviews)
        return 0

    def get_review_count(self):
        """Get total number of reviews"""
        return self.reviews.count()


class MovieGenre(models.Model):
    """Separate model for genres to support multiple genres per movie"""
    movie = models.ForeignKey(Movie, on_delete=models.CASCADE, related_name='movie_genres')
    genre_id = models.IntegerField()
    genre_name = models.CharField(max_length=50)

    class Meta:
        unique_together = ['movie', 'genre_id']

    def __str__(self):
        return f"{self.movie.title} - {self.genre_name}"


class MovieReview(models.Model):
    """User reviews and ratings for movies"""
    movie = models.ForeignKey(Movie, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='movie_reviews')
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    review_text = models.TextField(blank=True)
    is_spoiler = models.BooleanField(default=False)
    helpful_count = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['movie', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.movie.title} ({self.rating}/5)"


class MovieWishlist(models.Model):
    """User wishlist for movies"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='wishlist')
    movie = models.ForeignKey(Movie, on_delete=models.CASCADE, related_name='wishlisted_by')
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'movie']

    def __str__(self):
        return f"{self.user.username} - {self.movie.title}"


class MovieRecommendation(models.Model):
    """Movie recommendations based on user preferences"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recommendations')
    movie = models.ForeignKey(Movie, on_delete=models.CASCADE, related_name='recommended_to')
    score = models.FloatField(default=0.0)
    reason = models.CharField(max_length=200, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'movie']
        ordering = ['-score', '-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.movie.title} (Score: {self.score})"
