{% extends 'base.html' %}

{% block title %}My Bookings - Movie Ticket Booking{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>My Bookings</h2>
    <span class="text-muted">{{ bookings.count }} booking{{ bookings.count|pluralize }}</span>
</div>

{% if bookings %}
<div class="row">
    {% for booking in bookings %}
    <div class="col-md-6 mb-4">
        <div class="card booking-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h5 class="card-title">{{ booking.showtime.movie.title }}</h5>
                    <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% else %}danger{% endif %}">
                        {{ booking.get_status_display }}
                    </span>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Booking ID</small><br>
                        <strong>{{ booking.booking_id }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Amount</small><br>
                        <strong>₹{{ booking.total_amount }}</strong>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Theatre</small><br>
                        {{ booking.showtime.screen.theatre.name }}
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Screen</small><br>
                        {{ booking.showtime.screen.name }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <small class="text-muted">Date & Time</small><br>
                        {{ booking.showtime.date|date:"M d, Y" }}<br>
                        {{ booking.showtime.time|time:"g:i A" }}
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Seats</small><br>
                        {{ booking.seats }}
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <small class="text-muted">Booked on {{ booking.booking_date|date:"M d, Y g:i A" }}</small>
                    <div>
                        <a href="{% url 'bookings:booking_detail' booking.booking_id %}" 
                           class="btn btn-outline-primary btn-sm">View Details</a>
                        {% if booking.status == 'confirmed' %}
                        <a href="{% url 'bookings:cancel_booking' booking.booking_id %}" 
                           class="btn btn-outline-danger btn-sm">Cancel</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="text-center">
    <div class="alert alert-info">
        <i class="fas fa-ticket-alt fa-3x mb-3"></i>
        <h4>No bookings yet</h4>
        <p>You haven't made any bookings yet. Start by browsing our movies!</p>
        <a href="{% url 'movies:list' %}" class="btn btn-primary">Browse Movies</a>
    </div>
</div>
{% endif %}
{% endblock %}
