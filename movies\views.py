from django.shortcuts import render, get_object_or_404
from django.db.models import Q
from .models import Movie
from theatres.models import ShowTime
from datetime import date

def home(request):
    """Home page with featured movies"""
    featured_movies = Movie.objects.filter(is_active=True)[:6]
    context = {
        'featured_movies': featured_movies,
    }
    return render(request, 'movies/home.html', context)

def movie_list(request):
    """List all movies with filters"""
    movies = Movie.objects.filter(is_active=True)

    # Filter by genre
    genre = request.GET.get('genre')
    if genre:
        movies = movies.filter(genre=genre)

    # Filter by language
    language = request.GET.get('language')
    if language:
        movies = movies.filter(language=language)

    # Search by title
    search = request.GET.get('search')
    if search:
        movies = movies.filter(
            Q(title__icontains=search) |
            Q(description__icontains=search) |
            Q(cast__icontains=search) |
            Q(director__icontains=search)
        )

    context = {
        'movies': movies,
        'genres': Movie.GENRE_CHOICES,
        'languages': Movie.LANGUAGE_CHOICES,
        'current_genre': genre,
        'current_language': language,
        'search_query': search,
    }
    return render(request, 'movies/list.html', context)

def movie_detail(request, pk):
    """Movie detail page with showtimes"""
    movie = get_object_or_404(Movie, pk=pk, is_active=True)

    # Get showtimes for this movie (today and future)
    showtimes = ShowTime.objects.filter(
        movie=movie,
        date__gte=date.today(),
        is_active=True
    ).select_related('screen', 'screen__theatre').order_by('date', 'time')

    context = {
        'movie': movie,
        'showtimes': showtimes,
    }
    return render(request, 'movies/detail.html', context)
