from django.contrib import admin
from .models import Movie

@admin.register(Movie)
class MovieAdmin(admin.ModelAdmin):
    list_display = ['title', 'genre', 'language', 'duration', 'release_date', 'rating', 'is_active']
    list_filter = ['genre', 'language', 'is_active', 'release_date']
    search_fields = ['title', 'description', 'cast', 'director']
    list_editable = ['is_active', 'rating']
    date_hierarchy = 'release_date'
    ordering = ['-release_date']
