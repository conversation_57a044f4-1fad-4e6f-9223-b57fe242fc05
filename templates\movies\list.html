{% extends 'base.html' %}

{% block title %}Movies - Movie Ticket Booking{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <!-- Filters -->
        <div class="card">
            <div class="card-header">
                <h5>Filters</h5>
            </div>
            <div class="card-body">
                <form method="get">
                    <!-- Search -->
                    <div class="mb-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Movie title, cast, director...">
                    </div>
                    
                    <!-- Genre Filter -->
                    <div class="mb-3">
                        <label for="genre" class="form-label">Genre</label>
                        <select class="form-select" id="genre" name="genre">
                            <option value="">All Genres</option>
                            {% for value, label in genres %}
                            <option value="{{ value }}" {% if current_genre == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Language Filter -->
                    <div class="mb-3">
                        <label for="language" class="form-label">Language</label>
                        <select class="form-select" id="language" name="language">
                            <option value="">All Languages</option>
                            {% for value, label in languages %}
                            <option value="{{ value }}" {% if current_language == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Apply Filters</button>
                    <a href="{% url 'movies:list' %}" class="btn btn-outline-secondary w-100 mt-2">Clear Filters</a>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-9">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Movies</h2>
            <span class="text-muted">{{ movies.count }} movie{{ movies.count|pluralize }} found</span>
        </div>
        
        <div class="row">
            {% for movie in movies %}
            <div class="col-md-4 mb-4">
                <div class="card movie-card h-100">
                    {% if movie.poster %}
                    <img src="{{ movie.poster.url }}" class="card-img-top movie-poster" alt="{{ movie.title }}">
                    {% else %}
                    <div class="card-img-top movie-poster bg-secondary d-flex align-items-center justify-content-center">
                        <i class="fas fa-film fa-3x text-white"></i>
                    </div>
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ movie.title }}</h5>
                        <p class="card-text">{{ movie.description|truncatewords:15 }}</p>
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-primary">{{ movie.get_genre_display }}</span>
                                <span class="badge bg-secondary">{{ movie.get_language_display }}</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">{{ movie.duration }} min</small>
                                <small class="text-muted">{{ movie.release_date }}</small>
                            </div>
                            <a href="{% url 'movies:detail' movie.pk %}" class="btn btn-primary btn-sm w-100">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12 text-center">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No movies found matching your criteria.
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
