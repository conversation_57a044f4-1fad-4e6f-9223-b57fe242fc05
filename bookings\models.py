from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from theatres.models import ShowTime
import uuid

class Booking(models.Model):
    BOOKING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ]

    booking_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookings')
    showtime = models.ForeignKey(ShowTime, on_delete=models.CASCADE, related_name='bookings')
    seats = models.TextField(help_text="Comma-separated list of seat numbers")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=BOOKING_STATUS_CHOICES, default='confirmed')
    booking_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-booking_date']

    def __str__(self):
        return f"Booking {self.booking_id} - {self.user.username}"

    def get_seats_list(self):
        return [seat.strip() for seat in self.seats.split(',') if seat.strip()]

    def get_absolute_url(self):
        return reverse('bookings:booking_detail', kwargs={'booking_id': self.booking_id})

class BookedSeat(models.Model):
    showtime = models.ForeignKey(ShowTime, on_delete=models.CASCADE, related_name='booked_seats')
    seat_number = models.CharField(max_length=10)
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='seat_bookings')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['showtime', 'seat_number']

    def __str__(self):
        return f"{self.showtime} - Seat {self.seat_number}"
