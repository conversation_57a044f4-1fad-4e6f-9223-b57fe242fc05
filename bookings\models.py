from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from theatres.models import ShowTime
from decimal import Decimal
import uuid

class Booking(models.Model):
    BOOKING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
        ('expired', 'Expired'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('card', 'Credit/Debit Card'),
        ('upi', 'UPI'),
        ('netbanking', 'Net Banking'),
        ('wallet', 'Digital Wallet'),
        ('cash', 'Cash'),
    ]

    # Basic Information
    booking_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookings')
    showtime = models.ForeignKey(ShowTime, on_delete=models.CASCADE, related_name='bookings')
    seats = models.TextField(help_text="Comma-separated list of seat numbers")

    # Pricing Information
    ticket_price = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('250.00'))
    convenience_fee = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    taxes = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('250.00'))

    # Status Information
    status = models.CharField(max_length=20, choices=BOOKING_STATUS_CHOICES, default='pending')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, blank=True)

    # Payment Information
    payment_id = models.CharField(max_length=100, blank=True)
    payment_gateway_response = models.JSONField(blank=True, null=True)

    # Timestamps
    booking_date = models.DateTimeField(auto_now_add=True)
    payment_date = models.DateTimeField(null=True, blank=True)
    cancellation_date = models.DateTimeField(null=True, blank=True)

    # Additional Information
    booking_source = models.CharField(max_length=20, default='web')  # web, mobile, api
    special_requests = models.TextField(blank=True)
    promo_code = models.CharField(max_length=50, blank=True)

    # Auto-cancellation
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-booking_date']

    def __str__(self):
        return f"Booking {self.booking_id} - {self.user.username}"

    def get_seats_list(self):
        return [seat.strip() for seat in self.seats.split(',') if seat.strip()]

    def get_absolute_url(self):
        return reverse('bookings:booking_detail', kwargs={'booking_id': self.booking_id})

    def calculate_total_amount(self):
        """Calculate total amount including fees and taxes"""
        seat_count = len(self.get_seats_list())
        subtotal = self.ticket_price * seat_count
        total = subtotal + self.convenience_fee + self.taxes - self.discount_amount
        return max(total, Decimal('0.00'))

    def can_be_cancelled(self):
        """Check if booking can be cancelled"""
        if self.status in ['cancelled', 'refunded', 'expired']:
            return False

        # Can't cancel if show is within 2 hours
        show_datetime = timezone.make_aware(
            timezone.datetime.combine(self.showtime.date, self.showtime.time)
        )
        time_until_show = show_datetime - timezone.now()
        return time_until_show.total_seconds() > 7200  # 2 hours

    def get_cancellation_charges(self):
        """Calculate cancellation charges"""
        if not self.can_be_cancelled():
            return self.total_amount  # Full amount if can't cancel

        show_datetime = timezone.make_aware(
            timezone.datetime.combine(self.showtime.date, self.showtime.time)
        )
        time_until_show = show_datetime - timezone.now()
        hours_until_show = time_until_show.total_seconds() / 3600

        if hours_until_show > 24:
            return self.total_amount * Decimal('0.1')  # 10% charges
        elif hours_until_show > 4:
            return self.total_amount * Decimal('0.25')  # 25% charges
        else:
            return self.total_amount * Decimal('0.5')   # 50% charges

    def get_refund_amount(self):
        """Calculate refund amount after cancellation charges"""
        return self.total_amount - self.get_cancellation_charges()

    def is_expired(self):
        """Check if booking has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def save(self, *args, **kwargs):
        """Override save to calculate total amount"""
        if not self.total_amount:
            self.total_amount = self.calculate_total_amount()

        # Set expiration time for pending bookings (15 minutes)
        if self.status == 'pending' and not self.expires_at:
            self.expires_at = timezone.now() + timezone.timedelta(minutes=15)

        super().save(*args, **kwargs)

class BookedSeat(models.Model):
    showtime = models.ForeignKey(ShowTime, on_delete=models.CASCADE, related_name='booked_seats')
    seat_number = models.CharField(max_length=10)
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='seat_bookings')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['showtime', 'seat_number']

    def __str__(self):
        return f"{self.showtime} - Seat {self.seat_number}"


class PromoCode(models.Model):
    """Promotional codes for discounts"""
    code = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=200)
    discount_type = models.CharField(max_length=20, choices=[
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount')
    ])
    discount_value = models.DecimalField(max_digits=8, decimal_places=2)
    min_amount = models.DecimalField(max_digits=8, decimal_places=2, default=Decimal('0.00'))
    max_discount = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    usage_limit = models.PositiveIntegerField(null=True, blank=True)
    used_count = models.PositiveIntegerField(default=0)
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.code} - {self.discount_value}{'%' if self.discount_type == 'percentage' else '₹'}"

    def is_valid(self):
        """Check if promo code is valid"""
        now = timezone.now()
        if not self.is_active:
            return False
        if now < self.valid_from or now > self.valid_until:
            return False
        if self.usage_limit and self.used_count >= self.usage_limit:
            return False
        return True

    def calculate_discount(self, amount):
        """Calculate discount amount for given total"""
        if not self.is_valid() or amount < self.min_amount:
            return Decimal('0.00')

        if self.discount_type == 'percentage':
            discount = amount * (self.discount_value / 100)
            if self.max_discount:
                discount = min(discount, self.max_discount)
        else:
            discount = self.discount_value

        return min(discount, amount)


class BookingHistory(models.Model):
    """Track booking status changes"""
    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='history')
    status = models.CharField(max_length=20)
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'Booking histories'

    def __str__(self):
        return f"{self.booking.booking_id} - {self.status}"


class BookingNotification(models.Model):
    """Notifications related to bookings"""
    NOTIFICATION_TYPES = [
        ('booking_confirmed', 'Booking Confirmed'),
        ('payment_successful', 'Payment Successful'),
        ('booking_cancelled', 'Booking Cancelled'),
        ('refund_processed', 'Refund Processed'),
        ('show_reminder', 'Show Reminder'),
        ('booking_expired', 'Booking Expired'),
    ]

    booking = models.ForeignKey(Booking, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=30, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    is_sent = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.booking.booking_id} - {self.notification_type}"


class UserBookingPreference(models.Model):
    """User preferences for booking"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='booking_preferences')
    preferred_seat_type = models.CharField(max_length=20, choices=[
        ('front', 'Front'),
        ('middle', 'Middle'),
        ('back', 'Back'),
        ('aisle', 'Aisle'),
    ], default='middle')
    preferred_theatres = models.ManyToManyField('theatres.Theatre', blank=True)
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=True)
    push_notifications = models.BooleanField(default=True)
    auto_select_seats = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - Booking Preferences"
