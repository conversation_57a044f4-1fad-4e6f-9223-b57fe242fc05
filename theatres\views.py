from django.shortcuts import render, get_object_or_404
from .models import Theatre, ShowTime
from datetime import date

def theatre_list(request):
    """List all theatres"""
    theatres = Theatre.objects.filter(is_active=True)
    return render(request, 'theatres/list.html', {'theatres': theatres})

def theatre_detail(request, pk):
    """Theatre detail with current showtimes"""
    theatre = get_object_or_404(Theatre, pk=pk, is_active=True)

    # Get current and future showtimes for this theatre
    showtimes = ShowTime.objects.filter(
        screen__theatre=theatre,
        date__gte=date.today(),
        is_active=True
    ).select_related('movie', 'screen').order_by('date', 'time')

    context = {
        'theatre': theatre,
        'showtimes': showtimes,
    }
    return render(request, 'theatres/detail.html', context)

def showtime_list(request):
    """List all current showtimes"""
    showtimes = ShowTime.objects.filter(
        date__gte=date.today(),
        is_active=True
    ).select_related('movie', 'screen', 'screen__theatre').order_by('date', 'time')

    return render(request, 'theatres/showtime_list.html', {'showtimes': showtimes})
