{% extends 'base.html' %}

{% block title %}Password Reset - Movie Ticket Booking{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3>Reset Password</h3>
            </div>
            <div class="card-body">
                <p>Enter your email address and we'll send you a link to reset your password.</p>
                
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        {{ form.errors }}
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                        {{ form.email }}
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Send Reset Link</button>
                </form>
                
                <div class="text-center mt-3">
                    <p><a href="{% url 'accounts:login' %}">Back to Login</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
#id_email {
    width: 100%;
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
</style>
{% endblock %}
