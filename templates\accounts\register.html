{% extends 'base.html' %}

{% block title %}Register - Movie Ticket Booking{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h3>Create Account</h3>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        {% for field, errors in form.errors.items %}
                            {% for error in errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                        {{ form.username }}
                        {% if form.username.help_text %}
                        <div class="form-text">{{ form.username.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                        {{ form.password1 }}
                        {% if form.password1.help_text %}
                        <div class="form-text">{{ form.password1.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                        {{ form.password2 }}
                        {% if form.password2.help_text %}
                        <div class="form-text">{{ form.password2.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">Create Account</button>
                </form>
                
                <div class="text-center mt-3">
                    <p>Already have an account? <a href="{% url 'accounts:login' %}">Login here</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
#id_username, #id_password1, #id_password2 {
    width: 100%;
    padding: 0.375rem 0.75rem;
    margin-bottom: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
</style>
{% endblock %}
