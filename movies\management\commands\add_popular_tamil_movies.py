from django.core.management.base import BaseCommand
from movies.models import Movie
from datetime import date
from django.utils import timezone
from django.db.models import Q

class Command(BaseCommand):
    help = 'Add popular Tamil movies (2022-2024) with real data'

    def handle(self, *args, **options):
        self.stdout.write('Adding popular Tamil movies...')

        # Popular Tamil movies from 2022-2024
        tamil_movies = [
            {
                'title': '<PERSON><PERSON><PERSON>',
                'original_title': 'விக்ரம்',
                'description': 'A black-ops agent goes on a mission to hunt down a drug cartel leader with the help of a group of commandos.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 174,
                'release_date': date(2022, 6, 3),
                'cast': '<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>',
                'director': '<PERSON><PERSON>',
                'rating': 8.2,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.2,
                'vote_count': 45000,
                'popularity': 92.5,
                'tagline': 'Once upon a time in South India',
                'tmdb_id': 975902,
                'poster_path': 'https://image.tmdb.org/t/p/w500/vVpEOvdxVBP2aV166j5Xlvb5Cdc.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/7ZO9yoEU2fAHKhmJWfAqNQUJ5t7.jpg'
            },
            {
                'title': 'RRR',
                'original_title': 'RRR',
                'description': 'A fictional story about two legendary revolutionaries and their journey away from home before they started fighting for their country in 1920s.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'te',
                'duration': 187,
                'release_date': date(2022, 3, 25),
                'cast': 'N.T. Rama Rao Jr., Ram Charan, Alia Bhatt, Ajay Devgn, Olivia Morris',
                'director': 'S.S. Rajamouli',
                'rating': 8.8,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.8,
                'vote_count': 89000,
                'popularity': 95.2,
                'tagline': 'Rise Roar Revolt',
                'tmdb_id': 579974,
                'poster_path': 'https://image.tmdb.org/t/p/w500/wD6jOtgAhVKSZOnWPC8WkeJhJuS.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/8rpDcsfLJypbO6vREc0547VKqEv.jpg'
            },
            {
                'title': 'Beast',
                'original_title': 'பீஸ்ட்',
                'description': 'A RAW agent is in search of his captured team and the truth behind their disappearance.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 155,
                'release_date': date(2022, 4, 13),
                'cast': 'Vijay, Pooja Hegde, Selvaraghavan, Shine Tom Chacko, Yogi Babu',
                'director': 'Nelson Dilipkumar',
                'rating': 6.1,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 6.1,
                'vote_count': 12000,
                'popularity': 78.3,
                'tagline': 'Hunt begins',
                'tmdb_id': 940721,
                'poster_path': 'https://image.tmdb.org/t/p/w500/4OTYefcAlaShn6TGVK33UxLW9R7.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/4O5xeDIEduzV5CTXxGBbp1XwqXF.jpg'
            },
            {
                'title': 'Ponniyin Selvan: Part I',
                'original_title': 'பொன்னியின் செல்வன்: பாகம் 1',
                'description': 'Vandiyathevan crosses the Chola land to deliver a message from the Crown Prince Aditha Karikalan.',
                'genre': 'drama',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 167,
                'release_date': date(2022, 9, 30),
                'cast': 'Vikram, Aishwarya Rai Bachchan, Jayam Ravi, Karthi, Trisha',
                'director': 'Mani Ratnam',
                'rating': 7.6,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 7.6,
                'vote_count': 25000,
                'popularity': 85.7,
                'tagline': 'The golden era begins',
                'tmdb_id': 1003579,
                'poster_path': 'https://image.tmdb.org/t/p/w500/qGjGFjaNJJoJYKNNJlXjVXpU4Id.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/t5zCBSB5xMDKcDqe91qahCOUYVV.jpg'
            },
            {
                'title': 'Varisu',
                'original_title': 'வாரிசு',
                'description': 'Vijay, the youngest son of a business tycoon, tries to bring his family together while dealing with business rivals.',
                'genre': 'drama',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 169,
                'release_date': date(2023, 1, 11),
                'cast': 'Vijay, Rashmika Mandanna, R. Sarathkumar, Prabhu, Shaam',
                'director': 'Vamshi Paidipally',
                'rating': 6.1,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 6.1,
                'vote_count': 8500,
                'popularity': 72.4,
                'tagline': 'Family comes first',
                'tmdb_id': 1072790,
                'poster_path': 'https://image.tmdb.org/t/p/w500/rqgeBNWXas1yrAyXxwi9CahfScx.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/faXT8V80JRhnArTAeYXz0Eutpv9.jpg'
            },
            {
                'title': 'Thunivu',
                'original_title': 'துணிவு',
                'description': 'A mysterious mastermind - Daredevil and his team forms a plan and commits bank heist to find the corporate looted people\'s money.',
                'genre': 'thriller',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 146,
                'release_date': date(2023, 1, 11),
                'cast': 'Ajith Kumar, Manju Warrier, Samuthirakani, John Kokken, Veera',
                'director': 'H. Vinoth',
                'rating': 6.8,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 6.8,
                'vote_count': 7200,
                'popularity': 69.1,
                'tagline': 'No guts, no glory',
                'tmdb_id': 1024546,
                'poster_path': 'https://image.tmdb.org/t/p/w500/voHUmluYmKyleFkTu3lOXQG702u.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/9Rq14Eyrf7Tu1xk0Pl7VcNbNh1n.jpg'
            },
            {
                'title': 'Jailer',
                'original_title': 'ஜெயிலர்',
                'description': 'A retired jailer goes on a manhunt to find his son\'s killers. But the road leads him to a familiar, albeit a bit darker place.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 168,
                'release_date': date(2023, 8, 10),
                'cast': 'Rajinikanth, Vinayakan, Ramya Krishnan, Vasanth Ravi, Tamannaah Bhatia',
                'director': 'Nelson Dilipkumar',
                'rating': 7.1,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 7.1,
                'vote_count': 15000,
                'popularity': 88.9,
                'tagline': 'Justice has no retirement',
                'tmdb_id': 1138194,
                'poster_path': 'https://image.tmdb.org/t/p/w500/1ZWKGfAVcvNjgKaGVNhCxVNlwZr.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/uoVWgbSLZnEGOGF1fUXmYB4CYXW.jpg'
            },
            {
                'title': 'Leo',
                'original_title': 'லியோ',
                'description': 'Parthiban has a loving family and a quiet life. But when his past catches up with him, he must fight to protect his family.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 164,
                'release_date': date(2023, 10, 19),
                'cast': 'Vijay, Trisha, Sanjay Dutt, Arjun Sarja, Gautham Vasudev Menon',
                'director': 'Lokesh Kanagaraj',
                'rating': 7.3,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 7.3,
                'vote_count': 18000,
                'popularity': 91.2,
                'tagline': 'Blood is thicker than water',
                'tmdb_id': 1151534,
                'poster_path': 'https://image.tmdb.org/t/p/w500/pD6sL4vntUOXHmuvJPPZAgvyfd9.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/ctMserH8g2SeOAnCw5gFjdQF8mo.jpg'
            },
            {
                'title': 'Jawan',
                'original_title': 'जवान',
                'description': 'A high-octane action thriller which outlines the emotional journey of a man who is set to rectify the wrongs in the society.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'hi',
                'duration': 169,
                'release_date': date(2023, 9, 7),
                'cast': 'Shah Rukh Khan, Nayanthara, Vijay Sethupathi, Deepika Padukone, Priyamani',
                'director': 'Atlee',
                'rating': 7.0,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 7.0,
                'vote_count': 22000,
                'popularity': 86.5,
                'tagline': 'Ready for action',
                'tmdb_id': 1003581,
                'poster_path': 'https://image.tmdb.org/t/p/w500/5ScPNT6fHtfYJeWBajZciPV3hEL.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/8pjWz2lt29KyVGoq1mXYu6Br7dE.jpg'
            },
            {
                'title': 'Kaithi',
                'original_title': 'கைதி',
                'description': 'Dilli, an ex-convict, endeavours to meet his daughter for the first time after leaving prison. However, his plans are interrupted due to a drug raid.',
                'genre': 'action',
                'language': 'tamil',
                'original_language': 'ta',
                'duration': 145,
                'release_date': date(2019, 10, 25),
                'cast': 'Karthi, Narain, Arjun Das, Harish Uthaman, George Maryan',
                'director': 'Lokesh Kanagaraj',
                'rating': 8.4,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.4,
                'vote_count': 35000,
                'popularity': 89.7,
                'tagline': 'One night. One mission.',
                'tmdb_id': 625279,
                'poster_path': 'https://image.tmdb.org/t/p/w500/aBQkHOWVeYNdYWNdHRdY8BWnN0z.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/yEinVojJHsK7RMZd2ohDKqtOzQd.jpg'
            }
        ]

        created_count = 0
        updated_count = 0

        for movie_data in tamil_movies:
            # Check if movie already exists by title or TMDB ID
            existing_movie = Movie.objects.filter(
                Q(title=movie_data['title']) |
                Q(tmdb_id=movie_data.get('tmdb_id'))
            ).first()

            if existing_movie:
                # Update existing movie with new data
                for key, value in movie_data.items():
                    if value:  # Only update non-empty values
                        setattr(existing_movie, key, value)
                existing_movie.save()
                updated_count += 1
                self.stdout.write(f'✓ Updated: {existing_movie.title}')
            else:
                # Create new movie
                movie = Movie.objects.create(**movie_data)
                created_count += 1
                self.stdout.write(f'✓ Added: {movie.title} ({movie.release_date.year})')

        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎬 Tamil movies added successfully!\n'
                f'Added: {created_count} new movies\n'
                f'Updated: {updated_count} existing movies'
            )
        )
