from django.core.management.base import BaseCommand
from movies.services import TMDBService
from movies.models import Movie
from datetime import datetime
from django.utils import timezone
import logging
import time

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Sync all movies from TMDB API across multiple categories and languages'

    def add_arguments(self, parser):
        parser.add_argument(
            '--pages',
            type=int,
            default=5,
            help='Number of pages to fetch per category (default: 5)'
        )
        parser.add_argument(
            '--languages',
            type=str,
            default='en,hi,ta,te,ml,kn',
            help='Comma-separated language codes (default: en,hi,ta,te,ml,kn)'
        )
        parser.add_argument(
            '--categories',
            type=str,
            default='popular,now_playing,top_rated,upcoming',
            help='Comma-separated categories (default: popular,now_playing,top_rated,upcoming)'
        )
        parser.add_argument(
            '--min-votes',
            type=int,
            default=10,
            help='Minimum vote count for movies (default: 10)'
        )
        parser.add_argument(
            '--delay',
            type=float,
            default=0.5,
            help='Delay between API calls in seconds (default: 0.5)'
        )

    def handle(self, *args, **options):
        pages = options['pages']
        languages = options['languages'].split(',')
        categories = options['categories'].split(',')
        min_votes = options['min_votes']
        delay = options['delay']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🎬 Starting comprehensive movie sync from TMDB API\n'
                f'Languages: {", ".join(languages)}\n'
                f'Categories: {", ".join(categories)}\n'
                f'Pages per category: {pages}\n'
                f'Minimum votes: {min_votes}'
            )
        )
        
        tmdb = TMDBService()
        
        # Check if API key is configured
        if not tmdb.api_key:
            self.stdout.write(
                self.style.ERROR('❌ TMDB API key not configured. Please set TMDB_API_KEY in your .env file.')
            )
            return
        
        total_added = 0
        total_updated = 0
        total_errors = 0
        
        try:
            # Sync movies by categories (global)
            for category in categories:
                self.stdout.write(f'\n📂 Syncing {category} movies...')
                added, updated, errors = self._sync_category(tmdb, category, pages, min_votes, delay)
                total_added += added
                total_updated += updated
                total_errors += errors
            
            # Sync movies by languages
            for language in languages:
                language = language.strip()
                self.stdout.write(f'\n🌍 Syncing {language} movies...')
                added, updated, errors = self._sync_language(tmdb, language, pages, min_votes, delay)
                total_added += added
                total_updated += updated
                total_errors += errors
            
            # Sync trending movies
            self.stdout.write(f'\n🔥 Syncing trending movies...')
            added, updated, errors = self._sync_trending(tmdb, pages, delay)
            total_added += added
            total_updated += updated
            total_errors += errors
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n🎉 Movie sync completed!\n'
                    f'✅ Added: {total_added} new movies\n'
                    f'🔄 Updated: {total_updated} existing movies\n'
                    f'❌ Errors: {total_errors} movies failed\n'
                    f'📊 Total movies in database: {Movie.objects.count()}'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during sync: {e}')
            )
    
    def _sync_category(self, tmdb, category, pages, min_votes, delay):
        """Sync movies from a specific category"""
        added = 0
        updated = 0
        errors = 0
        
        for page in range(1, pages + 1):
            self.stdout.write(f'  📄 Fetching {category} page {page}...')
            
            try:
                # Get movies based on category
                if category == 'popular':
                    data = tmdb.get_popular_movies(page)
                elif category == 'now_playing':
                    data = tmdb.get_now_playing_movies(page)
                elif category == 'top_rated':
                    data = tmdb.get_top_rated_movies(page)
                elif category == 'upcoming':
                    data = tmdb.get_upcoming_movies(page)
                else:
                    continue
                
                if not data or 'results' not in data:
                    self.stdout.write(f'    ⚠️ No results for {category} page {page}')
                    continue
                
                movies = data['results']
                self.stdout.write(f'    📽️ Found {len(movies)} movies')
                
                for tmdb_movie in movies:
                    if tmdb_movie.get('vote_count', 0) < min_votes:
                        continue
                    
                    result = self._process_movie(tmdb, tmdb_movie)
                    if result == 'added':
                        added += 1
                    elif result == 'updated':
                        updated += 1
                    elif result == 'error':
                        errors += 1
                
                # Rate limiting
                time.sleep(delay)
                
            except Exception as e:
                self.stdout.write(f'    ❌ Error fetching {category} page {page}: {e}')
                errors += 1
        
        return added, updated, errors
    
    def _sync_language(self, tmdb, language_code, pages, min_votes, delay):
        """Sync movies from a specific language"""
        added = 0
        updated = 0
        errors = 0
        
        for page in range(1, pages + 1):
            self.stdout.write(f'  📄 Fetching {language_code} movies page {page}...')
            
            try:
                # Use discover endpoint for language-specific movies
                data = tmdb.discover_movies(
                    with_original_language=language_code,
                    sort_by='popularity.desc',
                    page=page,
                    vote_count_gte=min_votes
                )
                
                if not data or 'results' not in data:
                    self.stdout.write(f'    ⚠️ No results for {language_code} page {page}')
                    continue
                
                movies = data['results']
                self.stdout.write(f'    📽️ Found {len(movies)} {language_code} movies')
                
                for tmdb_movie in movies:
                    result = self._process_movie(tmdb, tmdb_movie)
                    if result == 'added':
                        added += 1
                    elif result == 'updated':
                        updated += 1
                    elif result == 'error':
                        errors += 1
                
                # Rate limiting
                time.sleep(delay)
                
            except Exception as e:
                self.stdout.write(f'    ❌ Error fetching {language_code} page {page}: {e}')
                errors += 1
        
        return added, updated, errors
    
    def _sync_trending(self, tmdb, pages, delay):
        """Sync trending movies"""
        added = 0
        updated = 0
        errors = 0
        
        for page in range(1, min(pages, 3) + 1):  # Trending usually has fewer pages
            self.stdout.write(f'  📄 Fetching trending movies page {page}...')
            
            try:
                # Use discover with recent releases and high popularity
                data = tmdb.discover_movies(
                    sort_by='popularity.desc',
                    page=page,
                    primary_release_date_gte='2020-01-01',
                    vote_count_gte=50
                )
                
                if not data or 'results' not in data:
                    continue
                
                movies = data['results']
                self.stdout.write(f'    📽️ Found {len(movies)} trending movies')
                
                for tmdb_movie in movies:
                    result = self._process_movie(tmdb, tmdb_movie)
                    if result == 'added':
                        added += 1
                    elif result == 'updated':
                        updated += 1
                    elif result == 'error':
                        errors += 1
                
                # Rate limiting
                time.sleep(delay)
                
            except Exception as e:
                self.stdout.write(f'    ❌ Error fetching trending page {page}: {e}')
                errors += 1
        
        return added, updated, errors
    
    def _process_movie(self, tmdb, tmdb_movie):
        """Process a single movie from TMDB"""
        try:
            # Get detailed movie information
            movie_details = tmdb.get_movie_details(tmdb_movie['id'])
            if not movie_details:
                return 'error'
            
            # Get cast and crew information
            credits = tmdb.get_movie_credits(tmdb_movie['id'])
            
            # Format the movie data
            movie_data = self._format_movie_data(movie_details, credits, tmdb)
            
            if not movie_data:
                return 'error'
            
            # Check if movie already exists
            existing_movie = Movie.objects.filter(tmdb_id=movie_data['tmdb_id']).first()
            
            if existing_movie:
                # Update existing movie with latest data
                for key, value in movie_data.items():
                    if value:  # Only update non-empty values
                        setattr(existing_movie, key, value)
                existing_movie.save()
                self.stdout.write(f'    🔄 Updated: {existing_movie.title}')
                return 'updated'
            else:
                # Create new movie
                movie = Movie.objects.create(**movie_data)
                self.stdout.write(f'    ✅ Added: {movie.title} ({movie.release_date.year})')
                return 'added'
                
        except Exception as e:
            logger.error(f"Error processing movie {tmdb_movie.get('title', 'Unknown')}: {e}")
            self.stdout.write(f'    ❌ Error: {tmdb_movie.get("title", "Unknown")}')
            return 'error'
    
    def _format_movie_data(self, movie_details, credits, tmdb):
        """Format TMDB movie data for our Movie model"""
        try:
            # Extract cast information
            cast_list = []
            if credits and 'cast' in credits:
                cast_list = [actor['name'] for actor in credits['cast'][:10]]  # Top 10 actors
            
            # Extract director information
            director = 'Unknown'
            if credits and 'crew' in credits:
                for crew_member in credits['crew']:
                    if crew_member['job'] == 'Director':
                        director = crew_member['name']
                        break
            
            # Map TMDB genres to our genre choices
            genre_mapping = {
                28: 'action', 35: 'comedy', 18: 'drama', 27: 'horror',
                10749: 'romance', 53: 'thriller', 878: 'sci-fi', 14: 'fantasy',
                16: 'animation', 99: 'documentary'
            }
            
            # Get primary genre
            primary_genre = 'drama'  # default
            if movie_details.get('genres'):
                for genre in movie_details['genres']:
                    if genre['id'] in genre_mapping:
                        primary_genre = genre_mapping[genre['id']]
                        break
            
            # Map language
            language_mapping = {
                'en': 'english', 'hi': 'hindi', 'ta': 'tamil', 'te': 'telugu',
                'ml': 'malayalam', 'kn': 'kannada', 'bn': 'bengali', 'mr': 'marathi'
            }
            
            original_lang = movie_details.get('original_language', 'en')
            language = language_mapping.get(original_lang, 'english')
            
            # Parse release date
            release_date = timezone.now().date()
            if movie_details.get('release_date'):
                try:
                    release_date = datetime.strptime(
                        movie_details['release_date'], '%Y-%m-%d'
                    ).date()
                except ValueError:
                    pass
            
            # Calculate rating (convert from 10-point to 5-point scale)
            rating = round(movie_details.get('vote_average', 0) / 2, 1)
            
            return {
                'title': movie_details.get('title', ''),
                'original_title': movie_details.get('original_title', ''),
                'description': movie_details.get('overview', ''),
                'genre': primary_genre,
                'language': language,
                'original_language': original_lang,
                'duration': movie_details.get('runtime', 120),
                'release_date': release_date,
                'cast': ', '.join(cast_list) if cast_list else '',
                'director': director,
                'rating': rating,
                'tmdb_id': movie_details.get('id'),
                'imdb_id': movie_details.get('imdb_id', ''),
                'poster_path': tmdb.get_full_image_url(movie_details.get('poster_path')),
                'backdrop_path': tmdb.get_full_image_url(movie_details.get('backdrop_path')),
                'popularity': movie_details.get('popularity', 0),
                'vote_average': movie_details.get('vote_average', 0),
                'vote_count': movie_details.get('vote_count', 0),
                'adult': movie_details.get('adult', False),
                'budget': movie_details.get('budget'),
                'revenue': movie_details.get('revenue'),
                'tagline': movie_details.get('tagline', ''),
                'homepage': movie_details.get('homepage', ''),
                'is_featured': movie_details.get('vote_average', 0) >= 7.0,
                'is_now_showing': True,
                'is_coming_soon': False,
                'is_active': True,
            }
            
        except Exception as e:
            logger.error(f"Error formatting movie data: {e}")
            return None
