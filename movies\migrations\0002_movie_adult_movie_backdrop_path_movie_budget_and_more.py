# Generated by Django 4.2.7 on 2025-05-28 18:26

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('movies', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='movie',
            name='adult',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='movie',
            name='backdrop_path',
            field=models.URLField(blank=True, help_text='Backdrop image URL from TMDB'),
        ),
        migrations.AddField(
            model_name='movie',
            name='budget',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='movie',
            name='homepage',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='movie',
            name='imdb_id',
            field=models.Char<PERSON>ield(blank=True, help_text='IMDB Movie ID', max_length=20),
        ),
        migrations.AddField(
            model_name='movie',
            name='is_coming_soon',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='movie',
            name='is_featured',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='movie',
            name='is_now_showing',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='movie',
            name='original_language',
            field=models.CharField(blank=True, max_length=10),
        ),
        migrations.AddField(
            model_name='movie',
            name='original_title',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='movie',
            name='popularity',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='movie',
            name='poster_path',
            field=models.URLField(blank=True, help_text='Poster image URL from TMDB'),
        ),
        migrations.AddField(
            model_name='movie',
            name='revenue',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='movie',
            name='tagline',
            field=models.CharField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name='movie',
            name='tmdb_id',
            field=models.IntegerField(blank=True, help_text='TMDB Movie ID', null=True, unique=True),
        ),
        migrations.AddField(
            model_name='movie',
            name='trailer_url',
            field=models.URLField(blank=True, help_text='YouTube trailer URL'),
        ),
        migrations.AddField(
            model_name='movie',
            name='vote_average',
            field=models.FloatField(default=0.0),
        ),
        migrations.AddField(
            model_name='movie',
            name='vote_count',
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name='MovieWishlist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('movie', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlisted_by', to='movies.movie')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wishlist', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'movie')},
            },
        ),
        migrations.CreateModel(
            name='MovieReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(help_text='Rating from 1 to 5 stars', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('review_text', models.TextField(blank=True)),
                ('is_spoiler', models.BooleanField(default=False)),
                ('helpful_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('movie', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='movies.movie')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movie_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('movie', 'user')},
            },
        ),
        migrations.CreateModel(
            name='MovieRecommendation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('score', models.FloatField(default=0.0)),
                ('reason', models.CharField(blank=True, max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('movie', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommended_to', to='movies.movie')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-score', '-created_at'],
                'unique_together': {('user', 'movie')},
            },
        ),
        migrations.CreateModel(
            name='MovieGenre',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('genre_id', models.IntegerField()),
                ('genre_name', models.CharField(max_length=50)),
                ('movie', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movie_genres', to='movies.movie')),
            ],
            options={
                'unique_together': {('movie', 'genre_id')},
            },
        ),
    ]
