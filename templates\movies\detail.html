{% extends 'base.html' %}

{% block title %}{{ movie.title }} - Movie Ticket Booking{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        {% if movie.poster %}
        <img src="{{ movie.poster.url }}" class="img-fluid movie-detail-poster" alt="{{ movie.title }}">
        {% else %}
        <div class="movie-detail-poster bg-secondary d-flex align-items-center justify-content-center">
            <i class="fas fa-film fa-5x text-white"></i>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-8">
        <h1>{{ movie.title }}</h1>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <p><strong>Genre:</strong> {{ movie.get_genre_display }}</p>
                <p><strong>Language:</strong> {{ movie.get_language_display }}</p>
                <p><strong>Duration:</strong> {{ movie.duration }} minutes</p>
            </div>
            <div class="col-md-6">
                <p><strong>Release Date:</strong> {{ movie.release_date }}</p>
                <p><strong>Director:</strong> {{ movie.director }}</p>
                <p><strong>Rating:</strong> {{ movie.rating }}/10</p>
            </div>
        </div>
        
        <div class="mb-4">
            <h5>Synopsis</h5>
            <p>{{ movie.description }}</p>
        </div>
        
        <div class="mb-4">
            <h5>Cast</h5>
            <div class="d-flex flex-wrap">
                {% for cast_member in movie.get_cast_list %}
                <span class="badge bg-light text-dark me-2 mb-2">{{ cast_member }}</span>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Showtimes -->
<div class="mt-5">
    <h3>Showtimes</h3>
    {% if showtimes %}
    <div class="row">
        {% regroup showtimes by date as showtimes_by_date %}
        {% for date_group in showtimes_by_date %}
        <div class="col-12 mb-4">
            <h5 class="mb-3">{{ date_group.grouper|date:"l, F d, Y" }}</h5>
            <div class="row">
                {% regroup date_group.list by screen.theatre as showtimes_by_theatre %}
                {% for theatre_group in showtimes_by_theatre %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">{{ theatre_group.grouper.name }}</h6>
                            <small class="text-muted">{{ theatre_group.grouper.city }}</small>
                        </div>
                        <div class="card-body">
                            {% for showtime in theatre_group.list %}
                            <div class="showtime-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>{{ showtime.time|time:"g:i A" }}</strong>
                                        <br>
                                        <small class="text-muted">{{ showtime.screen.name }}</small>
                                    </div>
                                    <div class="text-end">
                                        <span class="price-tag">₹{{ showtime.price }}</span>
                                        <br>
                                        {% if user.is_authenticated %}
                                        <a href="{% url 'bookings:book_tickets' showtime.pk %}" 
                                           class="btn btn-primary btn-sm mt-1">Book Now</a>
                                        {% else %}
                                        <a href="{% url 'accounts:login' %}" 
                                           class="btn btn-outline-primary btn-sm mt-1">Login to Book</a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        No showtimes available for this movie at the moment.
    </div>
    {% endif %}
</div>
{% endblock %}
