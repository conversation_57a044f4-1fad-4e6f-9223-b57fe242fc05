# 🎬 Advanced Movie Ticket Booking System

A **complete Movie Ticket Booking System** built with **Django** and **SQLite**, enhanced with **real-time movie data from TMDB API**. This advanced system rivals platforms like **BookMyShow** and **TicketNew** with modern features and functionality.

## 🚀 Enhanced Features

### 🎯 **Real-time Movie Data**
- **TMDB API Integration**: Fetch latest movies, ratings, and details from The Movie Database
- **Auto-sync Popular Movies**: Automatically sync trending and popular movies
- **Rich Movie Information**: Posters, backdrops, trailers, cast, crew, and ratings
- **Multiple Categories**: Now Playing, Upcoming, Top Rated, and Popular movies

### 👤 **Advanced User Features**
- **Enhanced Authentication**: Registration, login, logout, password reset with email
- **User Profiles**: Comprehensive profile management with preferences
- **Movie Reviews & Ratings**: Users can rate and review movies (1-5 stars)
- **Wishlist**: Save movies to personal wishlist
- **Personalized Recommendations**: AI-powered movie recommendations
- **Booking History**: Complete booking history with detailed information
- **Advanced Search**: Search by title, cast, director, genre with filters

### 🎟️ **Advanced Booking System**
- **Interactive Seat Selection**: Visual seat map with real-time availability
- **Smart Pricing**: Dynamic pricing with convenience fees and taxes
- **Promo Codes**: Discount system with percentage and fixed amount coupons
- **Multiple Payment Options**: Card, UPI, Net Banking, Wallet support
- **Booking Expiration**: Auto-expire pending bookings after 15 minutes
- **Flexible Cancellation**: Time-based cancellation charges
- **Booking Notifications**: Email/SMS notifications for all booking events

### 🎬 Movie Management
- **Movie CRUD**: Add, edit, delete movies with poster uploads
- **Movie Information**: Title, description, genre, language, duration, cast, director, rating
- **Movie Filtering**: Filter by genre, language, and search by title/cast/director

### 🏢 Theatre & Showtime Management
- **Theatre Management**: Add and manage theatres with location details
- **Screen Management**: Configure screens with seat layouts
- **Showtime Scheduling**: Schedule movie showtimes with pricing

### 🎟️ Booking System
- **Interactive Seat Selection**: Visual seat map with availability status
- **Real-time Availability**: Prevent double booking with real-time seat status
- **Booking Confirmation**: Secure booking process with email confirmation
- **Booking History**: Complete booking history with status tracking

### 🔐 Admin Features
- **Admin Dashboard**: Comprehensive admin interface for all models
- **User Management**: Manage users and their profiles
- **Content Management**: Manage movies, theatres, screens, and showtimes
- **Booking Oversight**: View and manage all bookings

## 🛠️ Technology Stack

- **Backend**: Django 4.2.7 (Python)
- **Database**: SQLite (easily switchable to PostgreSQL/MySQL)
- **Frontend**: HTML5, CSS3, Bootstrap 5
- **Authentication**: Django's built-in authentication system
- **File Handling**: Django's file upload for movie posters
- **Email**: Django's email backend (console backend for development)

## 📦 Installation & Setup

### Prerequisites
- Python 3.8 or higher
- pip (Python package installer)

### Installation Steps

1. **Clone or download the project**
   ```bash
   cd "movie ticket booking"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run migrations**
   ```bash
   python manage.py migrate
   ```

4. **Create a superuser (admin)**
   ```bash
   python manage.py createsuperuser
   ```

5. **Start the development server**
   ```bash
   python manage.py runserver
   ```

6. **Access the application**
   - Main site: http://127.0.0.1:8000/
   - Admin panel: http://127.0.0.1:8000/admin/

## 📁 Project Structure

```
movie_ticket_booking/
├── manage.py
├── requirements.txt
├── README.md
├── db.sqlite3                 # SQLite database
├── movie_booking/             # Main project settings
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── accounts/                  # User management app
│   ├── models.py             # UserProfile model
│   ├── views.py              # Registration, login, profile views
│   ├── urls.py
│   └── admin.py
├── movies/                    # Movie management app
│   ├── models.py             # Movie model
│   ├── views.py              # Movie listing, detail views
│   ├── urls.py
│   └── admin.py
├── theatres/                  # Theatre & showtime management
│   ├── models.py             # Theatre, Screen, ShowTime models
│   ├── views.py              # Theatre and showtime views
│   ├── urls.py
│   └── admin.py
├── bookings/                  # Booking system
│   ├── models.py             # Booking, BookedSeat models
│   ├── views.py              # Booking process views
│   ├── urls.py
│   └── admin.py
├── templates/                 # HTML templates
│   ├── base.html
│   ├── movies/
│   ├── accounts/
│   ├── theatres/
│   └── bookings/
├── static/                    # CSS, images
│   └── css/
└── media/                     # Uploaded files (movie posters)
```

## 🎯 Usage Guide

### For Regular Users
1. **Register**: Create an account or login
2. **Browse Movies**: View available movies with filters
3. **Select Movie**: Click on a movie to see details and showtimes
4. **Book Tickets**: Choose showtime, select seats, and confirm booking
5. **Manage Bookings**: View booking history and cancel if needed

### For Administrators
1. **Access Admin Panel**: Login at `/admin/` with superuser credentials
2. **Add Movies**: Upload movie details including posters
3. **Setup Theatres**: Add theatres and configure screens
4. **Schedule Shows**: Create showtimes for movies
5. **Monitor Bookings**: View and manage all user bookings

## 🔧 Configuration

### Email Settings
The system uses console email backend for development. To use real email:

1. Update `settings.py`:
   ```python
   EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
   EMAIL_HOST = 'your-smtp-server.com'
   EMAIL_PORT = 587
   EMAIL_USE_TLS = True
   EMAIL_HOST_USER = '<EMAIL>'
   EMAIL_HOST_PASSWORD = 'your-password'
   ```

### Database Configuration
To switch to PostgreSQL or MySQL, update the `DATABASES` setting in `settings.py`.

## 🚀 Deployment

For production deployment:
1. Set `DEBUG = False` in settings.py
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Configure email backend
5. Set secure secret key
6. Use environment variables for sensitive settings

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support or questions, please create an issue in the project repository.
