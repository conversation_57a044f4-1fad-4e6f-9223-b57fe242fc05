from django.contrib import admin
from .models import Booking, BookedSeat

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ['booking_id', 'user', 'showtime', 'total_amount', 'status', 'booking_date']
    list_filter = ['status', 'booking_date', 'showtime__movie', 'showtime__screen__theatre']
    search_fields = ['booking_id', 'user__username', 'user__email', 'showtime__movie__title']
    list_editable = ['status']
    date_hierarchy = 'booking_date'
    ordering = ['-booking_date']
    readonly_fields = ['booking_id']

@admin.register(BookedSeat)
class BookedSeatAdmin(admin.ModelAdmin):
    list_display = ['showtime', 'seat_number', 'booking', 'created_at']
    list_filter = ['showtime__date', 'showtime__movie', 'showtime__screen__theatre']
    search_fields = ['seat_number', 'booking__booking_id', 'showtime__movie__title']
    ordering = ['-created_at']
