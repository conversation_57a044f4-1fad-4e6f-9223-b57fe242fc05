import requests
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class TMDBService:
    """Service class for interacting with The Movie Database (TMDB) API"""
    
    def __init__(self):
        self.api_key = settings.TMDB_API_KEY
        self.base_url = settings.TMDB_BASE_URL
        self.image_base_url = settings.TMDB_IMAGE_BASE_URL
        
    def _make_request(self, endpoint, params=None):
        """Make a request to TMDB API with caching"""
        if not self.api_key:
            logger.warning("TMDB API key not configured")
            return None
            
        if params is None:
            params = {}
        params['api_key'] = self.api_key
        
        # Create cache key
        cache_key = f"tmdb_{endpoint}_{hash(str(sorted(params.items())))}"
        
        # Try to get from cache first
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result
            
        try:
            url = f"{self.base_url}/{endpoint}"
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Cache the result for 5 minutes
            cache.set(cache_key, data, 300)
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"TMDB API request failed: {e}")
            return None
    
    def get_popular_movies(self, page=1):
        """Get popular movies from TMDB"""
        return self._make_request('movie/popular', {'page': page})
    
    def get_now_playing_movies(self, page=1):
        """Get now playing movies from TMDB"""
        return self._make_request('movie/now_playing', {'page': page})
    
    def get_upcoming_movies(self, page=1):
        """Get upcoming movies from TMDB"""
        return self._make_request('movie/upcoming', {'page': page})
    
    def get_top_rated_movies(self, page=1):
        """Get top rated movies from TMDB"""
        return self._make_request('movie/top_rated', {'page': page})
    
    def get_movie_details(self, movie_id):
        """Get detailed information about a specific movie"""
        return self._make_request(f'movie/{movie_id}')
    
    def get_movie_credits(self, movie_id):
        """Get cast and crew information for a movie"""
        return self._make_request(f'movie/{movie_id}/credits')
    
    def get_movie_videos(self, movie_id):
        """Get videos (trailers, teasers) for a movie"""
        return self._make_request(f'movie/{movie_id}/videos')
    
    def search_movies(self, query, page=1):
        """Search for movies by title"""
        return self._make_request('search/movie', {'query': query, 'page': page})
    
    def get_movie_recommendations(self, movie_id, page=1):
        """Get movie recommendations based on a movie"""
        return self._make_request(f'movie/{movie_id}/recommendations', {'page': page})
    
    def get_similar_movies(self, movie_id, page=1):
        """Get similar movies"""
        return self._make_request(f'movie/{movie_id}/similar', {'page': page})
    
    def get_genres(self):
        """Get list of movie genres"""
        return self._make_request('genre/movie/list')
    
    def discover_movies(self, **kwargs):
        """Discover movies with various filters"""
        return self._make_request('discover/movie', kwargs)
    
    def get_full_image_url(self, path, size='w500'):
        """Get full image URL from TMDB path"""
        if not path:
            return None
        return f"https://image.tmdb.org/t/p/{size}{path}"
    
    def get_youtube_trailer_url(self, movie_id):
        """Get YouTube trailer URL for a movie"""
        videos_data = self.get_movie_videos(movie_id)
        if videos_data and 'results' in videos_data:
            for video in videos_data['results']:
                if (video.get('type') == 'Trailer' and 
                    video.get('site') == 'YouTube'):
                    return f"https://www.youtube.com/watch?v={video['key']}"
        return None
    
    def format_movie_data(self, tmdb_movie):
        """Format TMDB movie data for our Movie model"""
        if not tmdb_movie:
            return None
            
        # Map TMDB genres to our genre choices
        genre_mapping = {
            28: 'action',
            35: 'comedy',
            18: 'drama',
            27: 'horror',
            10749: 'romance',
            53: 'thriller',
            878: 'sci-fi',
            14: 'fantasy',
            16: 'animation',
            99: 'documentary'
        }
        
        # Get primary genre
        primary_genre = 'drama'  # default
        if tmdb_movie.get('genre_ids'):
            for genre_id in tmdb_movie['genre_ids']:
                if genre_id in genre_mapping:
                    primary_genre = genre_mapping[genre_id]
                    break
        
        # Map language
        language_mapping = {
            'en': 'english',
            'hi': 'hindi',
            'ta': 'tamil',
            'te': 'telugu',
            'ml': 'malayalam',
            'kn': 'kannada',
            'bn': 'bengali',
            'mr': 'marathi'
        }
        
        original_lang = tmdb_movie.get('original_language', 'en')
        language = language_mapping.get(original_lang, 'english')
        
        # Parse release date
        release_date = None
        if tmdb_movie.get('release_date'):
            try:
                release_date = datetime.strptime(
                    tmdb_movie['release_date'], '%Y-%m-%d'
                ).date()
            except ValueError:
                release_date = timezone.now().date()
        
        return {
            'title': tmdb_movie.get('title', ''),
            'original_title': tmdb_movie.get('original_title', ''),
            'description': tmdb_movie.get('overview', ''),
            'genre': primary_genre,
            'language': language,
            'original_language': original_lang,
            'release_date': release_date or timezone.now().date(),
            'tmdb_id': tmdb_movie.get('id'),
            'imdb_id': tmdb_movie.get('imdb_id', ''),
            'poster_path': self.get_full_image_url(tmdb_movie.get('poster_path')),
            'backdrop_path': self.get_full_image_url(tmdb_movie.get('backdrop_path')),
            'popularity': tmdb_movie.get('popularity', 0),
            'vote_average': tmdb_movie.get('vote_average', 0),
            'vote_count': tmdb_movie.get('vote_count', 0),
            'adult': tmdb_movie.get('adult', False),
            'budget': tmdb_movie.get('budget'),
            'revenue': tmdb_movie.get('revenue'),
            'tagline': tmdb_movie.get('tagline', ''),
            'homepage': tmdb_movie.get('homepage', ''),
            'duration': tmdb_movie.get('runtime', 120),  # default 2 hours
        }


class MovieDataSyncService:
    """Service for syncing movie data from TMDB to our database"""
    
    def __init__(self):
        self.tmdb = TMDBService()
    
    def sync_popular_movies(self, pages=5):
        """Sync popular movies from TMDB"""
        from .models import Movie, MovieGenre
        
        synced_count = 0
        
        for page in range(1, pages + 1):
            data = self.tmdb.get_popular_movies(page)
            if not data or 'results' not in data:
                continue
                
            for tmdb_movie in data['results']:
                movie_data = self.tmdb.format_movie_data(tmdb_movie)
                if not movie_data:
                    continue
                
                # Get or create movie
                movie, created = Movie.objects.get_or_create(
                    tmdb_id=movie_data['tmdb_id'],
                    defaults=movie_data
                )
                
                if created:
                    synced_count += 1
                    logger.info(f"Synced movie: {movie.title}")
                    
                    # Add trailer URL
                    trailer_url = self.tmdb.get_youtube_trailer_url(movie.tmdb_id)
                    if trailer_url:
                        movie.trailer_url = trailer_url
                        movie.save()
                
                # Sync genres
                if tmdb_movie.get('genre_ids'):
                    genres_data = self.tmdb.get_genres()
                    if genres_data and 'genres' in genres_data:
                        genre_map = {g['id']: g['name'] for g in genres_data['genres']}
                        
                        for genre_id in tmdb_movie['genre_ids']:
                            if genre_id in genre_map:
                                MovieGenre.objects.get_or_create(
                                    movie=movie,
                                    genre_id=genre_id,
                                    defaults={'genre_name': genre_map[genre_id]}
                                )
        
        return synced_count
    
    def sync_movie_details(self, movie):
        """Sync detailed information for a specific movie"""
        if not movie.tmdb_id:
            return False
            
        details = self.tmdb.get_movie_details(movie.tmdb_id)
        if not details:
            return False
        
        # Update movie with detailed information
        formatted_data = self.tmdb.format_movie_data(details)
        if formatted_data:
            for key, value in formatted_data.items():
                if value is not None:
                    setattr(movie, key, value)
            
            # Get cast information
            credits = self.tmdb.get_movie_credits(movie.tmdb_id)
            if credits and 'cast' in credits:
                cast_names = [actor['name'] for actor in credits['cast'][:10]]  # Top 10 actors
                movie.cast = ', '.join(cast_names)
            
            # Get director information
            if credits and 'crew' in credits:
                directors = [person['name'] for person in credits['crew'] 
                           if person['job'] == 'Director']
                if directors:
                    movie.director = ', '.join(directors)
            
            movie.save()
            return True
        
        return False
