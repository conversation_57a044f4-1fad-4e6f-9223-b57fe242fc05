from django.core.management.base import BaseCommand
from movies.services import MovieDataSyncService, TMDBService
from movies.models import Movie

class Command(BaseCommand):
    help = 'Sync movies from TMDB API'

    def add_arguments(self, parser):
        parser.add_argument(
            '--pages',
            type=int,
            default=5,
            help='Number of pages to sync from TMDB (default: 5)'
        )
        parser.add_argument(
            '--category',
            type=str,
            default='popular',
            choices=['popular', 'now_playing', 'upcoming', 'top_rated'],
            help='Category of movies to sync (default: popular)'
        )
        parser.add_argument(
            '--update-existing',
            action='store_true',
            help='Update existing movies with latest data from TMDB'
        )

    def handle(self, *args, **options):
        pages = options['pages']
        category = options['category']
        update_existing = options['update_existing']
        
        self.stdout.write(f'Syncing {category} movies from TMDB ({pages} pages)...')
        
        sync_service = MovieDataSyncService()
        tmdb_service = TMDBService()
        
        try:
            if category == 'popular':
                synced_count = sync_service.sync_popular_movies(pages)
            elif category == 'now_playing':
                synced_count = self._sync_category(tmdb_service, 'now_playing', pages)
            elif category == 'upcoming':
                synced_count = self._sync_category(tmdb_service, 'upcoming', pages)
            elif category == 'top_rated':
                synced_count = self._sync_category(tmdb_service, 'top_rated', pages)
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully synced {synced_count} movies from TMDB')
            )
            
            if update_existing:
                self.stdout.write('Updating existing movies with detailed information...')
                updated_count = self._update_existing_movies(sync_service)
                self.stdout.write(
                    self.style.SUCCESS(f'Updated {updated_count} existing movies')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to sync movies from TMDB: {e}')
            )

    def _sync_category(self, tmdb_service, category, pages):
        """Sync movies from a specific TMDB category"""
        from movies.models import Movie, MovieGenre
        
        synced_count = 0
        
        for page in range(1, pages + 1):
            if category == 'now_playing':
                data = tmdb_service.get_now_playing_movies(page)
            elif category == 'upcoming':
                data = tmdb_service.get_upcoming_movies(page)
            elif category == 'top_rated':
                data = tmdb_service.get_top_rated_movies(page)
            else:
                continue
                
            if not data or 'results' not in data:
                continue
                
            for tmdb_movie in data['results']:
                movie_data = tmdb_service.format_movie_data(tmdb_movie)
                if not movie_data:
                    continue
                
                # Set category-specific flags
                if category == 'now_playing':
                    movie_data['is_now_showing'] = True
                    movie_data['is_coming_soon'] = False
                elif category == 'upcoming':
                    movie_data['is_now_showing'] = False
                    movie_data['is_coming_soon'] = True
                elif category == 'top_rated':
                    movie_data['is_featured'] = True
                
                # Get or create movie
                movie, created = Movie.objects.get_or_create(
                    tmdb_id=movie_data['tmdb_id'],
                    defaults=movie_data
                )
                
                if created:
                    synced_count += 1
                    self.stdout.write(f'Synced movie: {movie.title}')
                    
                    # Add trailer URL
                    trailer_url = tmdb_service.get_youtube_trailer_url(movie.tmdb_id)
                    if trailer_url:
                        movie.trailer_url = trailer_url
                        movie.save()
                
                # Sync genres
                if tmdb_movie.get('genre_ids'):
                    genres_data = tmdb_service.get_genres()
                    if genres_data and 'genres' in genres_data:
                        genre_map = {g['id']: g['name'] for g in genres_data['genres']}
                        
                        for genre_id in tmdb_movie['genre_ids']:
                            if genre_id in genre_map:
                                MovieGenre.objects.get_or_create(
                                    movie=movie,
                                    genre_id=genre_id,
                                    defaults={'genre_name': genre_map[genre_id]}
                                )
        
        return synced_count

    def _update_existing_movies(self, sync_service):
        """Update existing movies with detailed information from TMDB"""
        updated_count = 0
        movies_with_tmdb_id = Movie.objects.filter(tmdb_id__isnull=False)
        
        for movie in movies_with_tmdb_id:
            try:
                if sync_service.sync_movie_details(movie):
                    updated_count += 1
                    self.stdout.write(f'Updated movie: {movie.title}')
            except Exception as e:
                self.stdout.write(f'Failed to update {movie.title}: {e}')
        
        return updated_count
