{% extends 'base.html' %}

{% block title %}Home - Movie Ticket Booking{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <h1 class="display-4 fw-bold">Welcome to MovieBooking</h1>
        <p class="lead">Book your favorite movies with ease</p>
        <a href="{% url 'movies:list' %}" class="btn btn-light btn-lg">Browse Movies</a>
    </div>
</div>

<!-- Features Section -->
<div class="container my-5">
    <div class="row text-center">
        <div class="col-md-4">
            <div class="feature-icon">
                <i class="fas fa-film"></i>
            </div>
            <h4>Latest Movies</h4>
            <p>Watch the latest blockbusters and trending movies</p>
        </div>
        <div class="col-md-4">
            <div class="feature-icon">
                <i class="fas fa-couch"></i>
            </div>
            <h4>Comfortable Seats</h4>
            <p>Choose your preferred seats from our interactive seat map</p>
        </div>
        <div class="col-md-4">
            <div class="feature-icon">
                <i class="fas fa-ticket-alt"></i>
            </div>
            <h4>Easy Booking</h4>
            <p>Simple and secure online ticket booking process</p>
        </div>
    </div>
</div>

<!-- Featured Movies -->
<div class="container my-5">
    <h2 class="text-center mb-4">Featured Movies</h2>
    <div class="row">
        {% for movie in featured_movies %}
        <div class="col-md-4 mb-4">
            <div class="card movie-card h-100">
                {% if movie.poster %}
                <img src="{{ movie.poster.url }}" class="card-img-top movie-poster" alt="{{ movie.title }}">
                {% else %}
                <div class="card-img-top movie-poster bg-secondary d-flex align-items-center justify-content-center">
                    <i class="fas fa-film fa-3x text-white"></i>
                </div>
                {% endif %}
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ movie.title }}</h5>
                    <p class="card-text">{{ movie.description|truncatewords:20 }}</p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-primary">{{ movie.get_genre_display }}</span>
                            <span class="badge bg-secondary">{{ movie.get_language_display }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ movie.duration }} min</small>
                            <a href="{% url 'movies:detail' movie.pk %}" class="btn btn-primary btn-sm">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12 text-center">
            <p class="text-muted">No movies available at the moment.</p>
        </div>
        {% endfor %}
    </div>
    
    {% if featured_movies %}
    <div class="text-center mt-4">
        <a href="{% url 'movies:list' %}" class="btn btn-outline-primary">View All Movies</a>
    </div>
    {% endif %}
</div>
{% endblock %}
