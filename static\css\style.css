/* Custom styles for Movie Booking System */

body {
    background-color: #f8f9fa;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.movie-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.movie-poster {
    height: 300px;
    object-fit: cover;
}

.seat-layout {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.seat {
    width: 30px;
    height: 30px;
    margin: 2px;
    border: 2px solid #28a745;
    background: #fff;
    border-radius: 5px;
    display: inline-block;
    text-align: center;
    line-height: 26px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.seat.booked {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
    cursor: not-allowed;
}

.seat.selected {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.seat:hover:not(.booked) {
    background: #28a745;
    color: white;
}

.screen {
    background: linear-gradient(to bottom, #333, #666);
    color: white;
    text-align: center;
    padding: 10px;
    margin: 20px 0;
    border-radius: 20px;
    font-weight: bold;
}

.booking-summary {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.feature-icon {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.genre-filter, .language-filter {
    margin-bottom: 20px;
}

.filter-btn {
    margin: 5px;
}

.booking-card {
    border-left: 4px solid #007bff;
}

.status-confirmed {
    color: #28a745;
}

.status-pending {
    color: #ffc107;
}

.status-cancelled {
    color: #dc3545;
}

.admin-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    display: block;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card-title {
    color: #333;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.alert {
    border-radius: 10px;
    border: none;
}

.movie-detail-poster {
    max-height: 500px;
    object-fit: cover;
    border-radius: 10px;
}

.showtime-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    transition: all 0.2s;
}

.showtime-card:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.price-tag {
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
}
