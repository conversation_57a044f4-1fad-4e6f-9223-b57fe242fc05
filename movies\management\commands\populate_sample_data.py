from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from movies.models import Movie
from theatres.models import Theatre, Screen, ShowTime
from datetime import date, time, timedelta

class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create sample movies
        movies_data = [
            {
                'title': 'Avengers: Endgame',
                'description': 'The epic conclusion to the Infinity Saga that became a defining moment in cinematic history.',
                'genre': 'action',
                'language': 'english',
                'duration': 181,
                'release_date': date(2024, 5, 1),
                'cast': '<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>',
                'director': '<PERSON>, <PERSON>',
                'rating': 8.4
            },
            {
                'title': 'Spider-Man: No Way Home',
                'description': '<PERSON> seeks help from <PERSON> when his secret identity is revealed.',
                'genre': 'action',
                'language': 'english',
                'duration': 148,
                'release_date': date(2024, 4, 15),
                'cast': '<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>',
                'director': '<PERSON>',
                'rating': 8.2
            },
            {
                'title': 'RRR',
                'description': 'A fictional story about two legendary revolutionaries and their journey away from home.',
                'genre': 'action',
                'language': 'telugu',
                'duration': 187,
                'release_date': date(2024, 3, 25),
                'cast': 'N. T. <PERSON> <PERSON> <PERSON>., Ram <PERSON>ran, <PERSON>a Bhatt, <PERSON><PERSON> <PERSON>gn',
                'director': 'S. S. <PERSON>mo<PERSON>',
                'rating': 7.9
            },
            {
                'title': 'The Batman',
                'description': 'Batman ventures into Gotham City\'s underworld when a sadistic killer leaves behind a trail of cryptic clues.',
                'genre': 'action',
                'language': 'english',
                'duration': 176,
                'release_date': date(2024, 3, 4),
                'cast': 'Robert Pattinson, Zoë Kravitz, Paul Dano, Jeffrey Wright',
                'director': 'Matt Reeves',
                'rating': 7.8
            },
            {
                'title': 'Dune',
                'description': 'Feature adaptation of Frank Herbert\'s science fiction novel about the son of a noble family.',
                'genre': 'sci-fi',
                'language': 'english',
                'duration': 155,
                'release_date': date(2024, 2, 18),
                'cast': 'Timothée Chalamet, Rebecca Ferguson, Oscar Isaac, Josh Brolin',
                'director': 'Denis Villeneuve',
                'rating': 8.0
            }
        ]
        
        for movie_data in movies_data:
            movie, created = Movie.objects.get_or_create(
                title=movie_data['title'],
                defaults=movie_data
            )
            if created:
                self.stdout.write(f'Created movie: {movie.title}')
        
        # Create sample theatres
        theatres_data = [
            {
                'name': 'PVR Cinemas',
                'address': '123 Mall Road, City Center',
                'city': 'Mumbai',
                'phone': '+91-9876543210'
            },
            {
                'name': 'INOX Multiplex',
                'address': '456 Shopping Complex, Downtown',
                'city': 'Delhi',
                'phone': '+91-9876543211'
            },
            {
                'name': 'Cinepolis',
                'address': '789 Entertainment Hub, Tech City',
                'city': 'Bangalore',
                'phone': '+91-9876543212'
            }
        ]
        
        for theatre_data in theatres_data:
            theatre, created = Theatre.objects.get_or_create(
                name=theatre_data['name'],
                city=theatre_data['city'],
                defaults=theatre_data
            )
            if created:
                self.stdout.write(f'Created theatre: {theatre.name}')
                
                # Create screens for each theatre
                for i in range(1, 4):  # 3 screens per theatre
                    screen, created = Screen.objects.get_or_create(
                        theatre=theatre,
                        name=f'Screen {i}',
                        defaults={
                            'total_seats': 100,
                            'rows': 10,
                            'seats_per_row': 10
                        }
                    )
                    if created:
                        self.stdout.write(f'Created screen: {screen.name} for {theatre.name}')
        
        # Create sample showtimes
        movies = Movie.objects.all()
        screens = Screen.objects.all()
        
        if movies and screens:
            today = date.today()
            times = [time(10, 0), time(13, 30), time(17, 0), time(20, 30)]
            
            for i in range(7):  # Next 7 days
                show_date = today + timedelta(days=i)
                for movie in movies[:3]:  # First 3 movies
                    for screen in screens[:2]:  # First 2 screens
                        for show_time in times[:2]:  # First 2 time slots
                            showtime, created = ShowTime.objects.get_or_create(
                                movie=movie,
                                screen=screen,
                                date=show_date,
                                time=show_time,
                                defaults={'price': 250.00}
                            )
                            if created:
                                self.stdout.write(f'Created showtime: {movie.title} at {screen} on {show_date}')
        
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
