from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from movies.models import Movie
from movies.services import MovieDataSyncService
from theatres.models import Theatre, Screen, ShowTime
from bookings.models import PromoCode
from datetime import date, time, timedelta
from decimal import Decimal
from django.utils import timezone

class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')

        # Sync movies from TMDB API
        self.stdout.write('Syncing movies from TMDB...')
        sync_service = MovieDataSyncService()
        try:
            synced_count = sync_service.sync_popular_movies(pages=3)
            self.stdout.write(f'Synced {synced_count} movies from TMDB')
        except Exception as e:
            self.stdout.write(f'TMDB sync failed: {e}')
            self.stdout.write('Creating sample movies manually...')
            self._create_sample_movies()

        # Create sample theatres and screens
        self._create_sample_theatres()

        # Create sample showtimes
        self._create_sample_showtimes()

        # Create sample promo codes
        self._create_sample_promo_codes()

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))

    def _create_sample_movies(self):
        """Create sample movies manually if TMDB sync fails"""
        movies_data = [
            {
                'title': 'Avengers: Endgame',
                'description': 'The epic conclusion to the Infinity Saga that became a defining moment in cinematic history.',
                'genre': 'action',
                'language': 'english',
                'duration': 181,
                'release_date': date(2024, 5, 1),
                'cast': 'Robert Downey Jr., Chris Evans, Mark Ruffalo, Chris Hemsworth, Scarlett Johansson',
                'director': 'Anthony Russo, Joe Russo',
                'rating': 8.4
            },
            {
                'title': 'Spider-Man: No Way Home',
                'description': 'Peter Parker seeks help from Doctor Strange when his secret identity is revealed.',
                'genre': 'action',
                'language': 'english',
                'duration': 148,
                'release_date': date(2024, 4, 15),
                'cast': 'Tom Holland, Zendaya, Benedict Cumberbatch, Jacob Batalon',
                'director': 'Jon Watts',
                'rating': 8.2
            },
            {
                'title': 'RRR',
                'description': 'A fictional story about two legendary revolutionaries and their journey away from home.',
                'genre': 'action',
                'language': 'telugu',
                'duration': 187,
                'release_date': date(2024, 3, 25),
                'cast': 'N. T. Rama Rao Jr., Ram Charan, Alia Bhatt, Ajay Devgn',
                'director': 'S. S. Rajamouli',
                'rating': 7.9
            },
            {
                'title': 'The Batman',
                'description': 'Batman ventures into Gotham City\'s underworld when a sadistic killer leaves behind a trail of cryptic clues.',
                'genre': 'action',
                'language': 'english',
                'duration': 176,
                'release_date': date(2024, 3, 4),
                'cast': 'Robert Pattinson, Zoë Kravitz, Paul Dano, Jeffrey Wright',
                'director': 'Matt Reeves',
                'rating': 7.8
            },
            {
                'title': 'Dune',
                'description': 'Feature adaptation of Frank Herbert\'s science fiction novel about the son of a noble family.',
                'genre': 'sci-fi',
                'language': 'english',
                'duration': 155,
                'release_date': date(2024, 2, 18),
                'cast': 'Timothée Chalamet, Rebecca Ferguson, Oscar Isaac, Josh Brolin',
                'director': 'Denis Villeneuve',
                'rating': 8.0
            }
        ]

        for movie_data in movies_data:
            movie, created = Movie.objects.get_or_create(
                title=movie_data['title'],
                defaults=movie_data
            )
            if created:
                self.stdout.write(f'Created movie: {movie.title}')

    def _create_sample_theatres(self):
        """Create sample theatres and screens"""
        theatres_data = [
            {
                'name': 'PVR Cinemas',
                'address': '123 Mall Road, City Center',
                'city': 'Mumbai',
                'phone': '+91-9876543210'
            },
            {
                'name': 'INOX Multiplex',
                'address': '456 Shopping Complex, Downtown',
                'city': 'Delhi',
                'phone': '+91-9876543211'
            },
            {
                'name': 'Cinepolis',
                'address': '789 Entertainment Hub, Tech City',
                'city': 'Bangalore',
                'phone': '+91-9876543212'
            }
        ]

        for theatre_data in theatres_data:
            theatre, created = Theatre.objects.get_or_create(
                name=theatre_data['name'],
                city=theatre_data['city'],
                defaults=theatre_data
            )
            if created:
                self.stdout.write(f'Created theatre: {theatre.name}')

                # Create screens for each theatre
                for i in range(1, 4):  # 3 screens per theatre
                    screen, created = Screen.objects.get_or_create(
                        theatre=theatre,
                        name=f'Screen {i}',
                        defaults={
                            'total_seats': 100,
                            'rows': 10,
                            'seats_per_row': 10
                        }
                    )
                    if created:
                        self.stdout.write(f'Created screen: {screen.name} for {theatre.name}')

    def _create_sample_showtimes(self):
        """Create sample showtimes"""
        movies = Movie.objects.all()
        screens = Screen.objects.all()

        if movies and screens:
            today = date.today()
            times = [time(10, 0), time(13, 30), time(17, 0), time(20, 30)]

            for i in range(7):  # Next 7 days
                show_date = today + timedelta(days=i)
                for movie in movies[:3]:  # First 3 movies
                    for screen in screens[:2]:  # First 2 screens
                        for show_time in times[:2]:  # First 2 time slots
                            _, created = ShowTime.objects.get_or_create(
                                movie=movie,
                                screen=screen,
                                date=show_date,
                                time=show_time,
                                defaults={'price': 250.00}
                            )
                            if created:
                                self.stdout.write(f'Created showtime: {movie.title} at {screen} on {show_date}')

    def _create_sample_promo_codes(self):
        """Create sample promotional codes"""
        promo_codes = [
            {
                'code': 'WELCOME10',
                'description': 'Welcome offer - 10% off',
                'discount_type': 'percentage',
                'discount_value': Decimal('10.00'),
                'min_amount': Decimal('200.00'),
                'max_discount': Decimal('100.00'),
                'usage_limit': 100,
                'valid_from': timezone.now(),
                'valid_until': timezone.now() + timedelta(days=30),
            },
            {
                'code': 'FLAT50',
                'description': 'Flat ₹50 off on bookings',
                'discount_type': 'fixed',
                'discount_value': Decimal('50.00'),
                'min_amount': Decimal('300.00'),
                'usage_limit': 50,
                'valid_from': timezone.now(),
                'valid_until': timezone.now() + timedelta(days=15),
            },
            {
                'code': 'WEEKEND20',
                'description': 'Weekend special - 20% off',
                'discount_type': 'percentage',
                'discount_value': Decimal('20.00'),
                'min_amount': Decimal('500.00'),
                'max_discount': Decimal('200.00'),
                'usage_limit': 25,
                'valid_from': timezone.now(),
                'valid_until': timezone.now() + timedelta(days=7),
            }
        ]

        for promo_data in promo_codes:
            promo, created = PromoCode.objects.get_or_create(
                code=promo_data['code'],
                defaults=promo_data
            )
            if created:
                self.stdout.write(f'Created promo code: {promo.code}')
