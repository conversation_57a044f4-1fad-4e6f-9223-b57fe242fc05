# Generated by Django 4.2.7 on 2025-05-28 18:10

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Movie',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('description', models.TextField()),
                ('genre', models.CharField(choices=[('action', 'Action'), ('comedy', 'Comedy'), ('drama', 'Drama'), ('horror', 'Horror'), ('romance', 'Romance'), ('thriller', 'Thriller'), ('sci-fi', 'Science Fiction'), ('fantasy', 'Fantasy'), ('animation', 'Animation'), ('documentary', 'Documentary')], max_length=20)),
                ('language', models.CharField(choices=[('english', 'English'), ('hindi', 'Hindi'), ('tamil', 'Tamil'), ('telugu', 'Telugu'), ('malayalam', 'Malayalam'), ('kannada', 'Kannada'), ('bengali', 'Bengali'), ('marathi', 'Marathi')], max_length=20)),
                ('poster', models.ImageField(blank=True, null=True, upload_to='movie_posters/')),
                ('duration', models.PositiveIntegerField(help_text='Duration in minutes')),
                ('release_date', models.DateField()),
                ('cast', models.TextField(help_text='Comma-separated list of cast members')),
                ('director', models.CharField(max_length=100)),
                ('rating', models.DecimalField(decimal_places=1, default=0.0, max_digits=3)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['-release_date'],
            },
        ),
    ]
