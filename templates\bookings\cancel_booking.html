{% extends 'base.html' %}

{% block title %}Cancel Booking - {{ booking.booking_id }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header text-center">
                <h4 class="text-danger">Cancel Booking</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. Are you sure you want to cancel this booking?
                </div>
                
                <!-- Booking Summary -->
                <div class="mb-4">
                    <h6>Booking Summary</h6>
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Booking ID:</strong></td>
                            <td>{{ booking.booking_id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Movie:</strong></td>
                            <td>{{ booking.showtime.movie.title }}</td>
                        </tr>
                        <tr>
                            <td><strong>Theatre:</strong></td>
                            <td>{{ booking.showtime.screen.theatre.name }}</td>
                        </tr>
                        <tr>
                            <td><strong>Date & Time:</strong></td>
                            <td>{{ booking.showtime.date|date:"M d, Y" }} at {{ booking.showtime.time|time:"g:i A" }}</td>
                        </tr>
                        <tr>
                            <td><strong>Seats:</strong></td>
                            <td>{{ booking.seats }}</td>
                        </tr>
                        <tr>
                            <td><strong>Amount:</strong></td>
                            <td><strong>₹{{ booking.total_amount }}</strong></td>
                        </tr>
                    </table>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-danger">Yes, Cancel Booking</button>
                        <a href="{% url 'bookings:booking_detail' booking.booking_id %}" 
                           class="btn btn-secondary">No, Keep Booking</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
