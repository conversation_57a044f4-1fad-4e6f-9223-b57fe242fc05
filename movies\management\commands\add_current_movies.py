from django.core.management.base import BaseCommand
from movies.models import Movie
from theatres.models import Theatre, Screen, ShowTime
from bookings.models import PromoCode
from datetime import date, time, timedelta
from decimal import Decimal
from django.utils import timezone

class Command(BaseCommand):
    help = 'Add current popular movies (2024-2025)'

    def handle(self, *args, **options):
        self.stdout.write('Adding current popular movies...')
        
        # Current popular movies (2024-2025)
        current_movies = [
            {
                'title': 'Dune: Part Two',
                'description': '<PERSON> unites with <PERSON><PERSON> and the Fremen while seeking revenge against the conspirators who destroyed his family.',
                'genre': 'sci-fi',
                'language': 'english',
                'duration': 166,
                'release_date': date(2024, 3, 1),
                'cast': '<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>',
                'director': '<PERSON>',
                'rating': 8.8,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.8,
                'vote_count': 125000,
                'popularity': 95.5,
                'tagline': 'Long live the fighters.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/1pdfLvkbY9ohJlCjQH2CZjjYVvJ.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/xtUxbh9hGVco5qs3xKVrZx06D7P.jpg'
            },
            {
                'title': 'Oppenheimer',
                'description': 'The story of American scientist J. Robert Oppenheimer and his role in the development of the atomic bomb.',
                'genre': 'drama',
                'language': 'english',
                'duration': 180,
                'release_date': date(2023, 7, 21),
                'cast': 'Cillian Murphy, Emily Blunt, Matt Damon, Robert Downey Jr., Florence Pugh',
                'director': 'Christopher Nolan',
                'rating': 8.4,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.4,
                'vote_count': 98000,
                'popularity': 88.2,
                'tagline': 'The world forever changes.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/8Gxv8gSFCU0XGDykEGv7zR1n2ua.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/fm6KqXpk3M2HVveHwCrBSSBaO0V.jpg'
            },
            {
                'title': 'Spider-Man: Across the Spider-Verse',
                'description': 'Miles Morales catapults across the Multiverse, where he encounters a team of Spider-People charged with protecting its very existence.',
                'genre': 'animation',
                'language': 'english',
                'duration': 140,
                'release_date': date(2023, 6, 2),
                'cast': 'Shameik Moore, Hailee Steinfeld, Brian Tyree Henry, Luna Lauren Vélez',
                'director': 'Joaquim Dos Santos, Kemp Powers, Justin K. Thompson',
                'rating': 8.7,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.7,
                'vote_count': 87000,
                'popularity': 92.1,
                'tagline': 'It\'s how you wear the mask that matters.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/8Vt6mWEReuy4Of61Lnj5Xj704m8.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/nHf61UzkfFno5X1ofIhugCPus2R.jpg'
            },
            {
                'title': 'Guardians of the Galaxy Vol. 3',
                'description': 'Peter Quill, still reeling from the loss of Gamora, must rally his team around him to defend the universe along with protecting one of their own.',
                'genre': 'action',
                'language': 'english',
                'duration': 150,
                'release_date': date(2023, 5, 5),
                'cast': 'Chris Pratt, Zoe Saldana, Dave Bautista, Karen Gillan, Pom Klementieff',
                'director': 'James Gunn',
                'rating': 8.0,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.0,
                'vote_count': 76000,
                'popularity': 85.3,
                'tagline': 'Once more with feeling.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/r2J02Z2OpNTctfOSN1Ydgii51I3.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/5YZbUmjbMa3ClvSW1Wj3Gk9QVmY.jpg'
            },
            {
                'title': 'The Batman',
                'description': 'When a sadistic serial killer begins murdering key political figures in Gotham, Batman is forced to investigate the city\'s hidden corruption.',
                'genre': 'action',
                'language': 'english',
                'duration': 176,
                'release_date': date(2022, 3, 4),
                'cast': 'Robert Pattinson, Zoë Kravitz, Paul Dano, Jeffrey Wright, Colin Farrell',
                'director': 'Matt Reeves',
                'rating': 7.8,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 7.8,
                'vote_count': 89000,
                'popularity': 78.9,
                'tagline': 'Unmask the truth.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/ew7CDxjKOUWVcgvn9OtKsHWMEkB.jpg'
            },
            {
                'title': 'Top Gun: Maverick',
                'description': 'After thirty years, Maverick is still pushing the envelope as a top naval aviator, but must confront ghosts of his past.',
                'genre': 'action',
                'language': 'english',
                'duration': 131,
                'release_date': date(2022, 5, 27),
                'cast': 'Tom Cruise, Miles Teller, Jennifer Connelly, Jon Hamm, Glen Powell',
                'director': 'Joseph Kosinski',
                'rating': 8.3,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 8.3,
                'vote_count': 94000,
                'popularity': 91.7,
                'tagline': 'Feel the need... The need for speed.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/62HCnUTziyWcpDaBO2i1DX17ljH.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/odJ4hx6g6vBt4lBWKFD1tI8WS4x.jpg'
            },
            {
                'title': 'Avatar: The Way of Water',
                'description': 'Jake Sully lives with his newfound family formed on the extrasolar moon Pandora.',
                'genre': 'sci-fi',
                'language': 'english',
                'duration': 192,
                'release_date': date(2022, 12, 16),
                'cast': 'Sam Worthington, Zoe Saldana, Sigourney Weaver, Stephen Lang, Kate Winslet',
                'director': 'James Cameron',
                'rating': 7.6,
                'is_featured': True,
                'is_now_showing': True,
                'vote_average': 7.6,
                'vote_count': 82000,
                'popularity': 84.2,
                'tagline': 'Return to Pandora.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/t6HIqrRAclMCA60NsSmeqe9RmNV.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/s16H6tpK2utvwDtzZ8Qy4qm5Emw.jpg'
            },
            {
                'title': 'John Wick: Chapter 4',
                'description': 'With the price on his head ever increasing, John Wick uncovers a path to defeating The High Table.',
                'genre': 'action',
                'language': 'english',
                'duration': 169,
                'release_date': date(2023, 3, 24),
                'cast': 'Keanu Reeves, Donnie Yen, Bill Skarsgård, Laurence Fishburne, Hiroyuki Sanada',
                'director': 'Chad Stahelski',
                'rating': 7.7,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 7.7,
                'vote_count': 71000,
                'popularity': 79.8,
                'tagline': 'No way back, one way out.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/vZloFAK7NmvMGKE7VkF5UHaz0I.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/fqv8v6AycXKsivp1T5yKtLbGXce.jpg'
            },
            {
                'title': 'Fast X',
                'description': 'Over many missions and against impossible odds, Dom Toretto and his family have outsmarted and outdriven every foe.',
                'genre': 'action',
                'language': 'english',
                'duration': 141,
                'release_date': date(2023, 5, 19),
                'cast': 'Vin Diesel, Michelle Rodriguez, Tyrese Gibson, Ludacris, John Cena, Jason Momoa',
                'director': 'Louis Leterrier',
                'rating': 5.7,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 5.7,
                'vote_count': 45000,
                'popularity': 67.3,
                'tagline': 'The end of the road begins.',
                'poster_path': 'https://image.tmdb.org/t/p/w500/fiVW06jE7z9YnO4trhaMEdclSiC.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/4XM8DUTQb3lhLemJC51Jx4a2EuA.jpg'
            },
            {
                'title': 'Scream VI',
                'description': 'Following the latest Ghostface killings, the four survivors leave Woodsboro behind and start a fresh chapter.',
                'genre': 'horror',
                'language': 'english',
                'duration': 123,
                'release_date': date(2023, 3, 10),
                'cast': 'Melissa Barrera, Jenna Ortega, Jasmin Savoy Brown, Mason Gooding, Courteney Cox',
                'director': 'Matt Bettinelli-Olpin, Tyler Gillett',
                'rating': 6.5,
                'is_featured': False,
                'is_now_showing': True,
                'vote_average': 6.5,
                'vote_count': 38000,
                'popularity': 62.1,
                'tagline': 'Who will survive?',
                'poster_path': 'https://image.tmdb.org/t/p/w500/wDWwtvkRRlgTiUr6TyLSMX8FCuZ.jpg',
                'backdrop_path': 'https://image.tmdb.org/t/p/w500/44immBwzhDVyjn87b3x3l9mlhAD.jpg'
            }
        ]
        
        created_count = 0
        for movie_data in current_movies:
            movie, created = Movie.objects.get_or_create(
                title=movie_data['title'],
                defaults=movie_data
            )
            if created:
                created_count += 1
                self.stdout.write(f'✓ Added: {movie.title} ({movie.release_date.year})')
            else:
                self.stdout.write(f'- Already exists: {movie.title}')
        
        self.stdout.write(f'\n🎬 Added {created_count} new movies!')
        
        # Create sample theatres if they don't exist
        self._create_theatres_if_needed()
        
        # Create showtimes for the new movies
        self._create_showtimes_for_movies()
        
        # Create promo codes
        self._create_promo_codes()
        
        self.stdout.write(self.style.SUCCESS('\n🎉 Current movies database updated successfully!'))
    
    def _create_theatres_if_needed(self):
        """Create sample theatres if they don't exist"""
        if Theatre.objects.count() == 0:
            theatres_data = [
                {
                    'name': 'PVR Cinemas',
                    'address': '123 Mall Road, City Center',
                    'city': 'Mumbai',
                    'phone': '+91-9876543210'
                },
                {
                    'name': 'INOX Multiplex',
                    'address': '456 Shopping Complex, Downtown',
                    'city': 'Delhi',
                    'phone': '+91-9876543211'
                },
                {
                    'name': 'Cinepolis',
                    'address': '789 Entertainment Hub, Tech City',
                    'city': 'Bangalore',
                    'phone': '+91-9876543212'
                }
            ]
            
            for theatre_data in theatres_data:
                theatre = Theatre.objects.create(**theatre_data)
                self.stdout.write(f'Created theatre: {theatre.name}')
                
                # Create screens for each theatre
                for i in range(1, 4):  # 3 screens per theatre
                    screen = Screen.objects.create(
                        theatre=theatre,
                        name=f'Screen {i}',
                        total_seats=100,
                        rows=10,
                        seats_per_row=10
                    )
                    self.stdout.write(f'Created screen: {screen.name} for {theatre.name}')
    
    def _create_showtimes_for_movies(self):
        """Create showtimes for movies"""
        movies = Movie.objects.filter(is_now_showing=True)
        screens = Screen.objects.all()
        
        if movies and screens:
            today = date.today()
            times = [time(10, 0), time(13, 30), time(17, 0), time(20, 30)]
            
            showtime_count = 0
            for i in range(7):  # Next 7 days
                show_date = today + timedelta(days=i)
                for movie in movies[:5]:  # First 5 movies
                    for screen in screens[:2]:  # First 2 screens
                        for show_time in times[:2]:  # First 2 time slots
                            _, created = ShowTime.objects.get_or_create(
                                movie=movie,
                                screen=screen,
                                date=show_date,
                                time=show_time,
                                defaults={'price': 250.00}
                            )
                            if created:
                                showtime_count += 1
            
            self.stdout.write(f'Created {showtime_count} showtimes')
    
    def _create_promo_codes(self):
        """Create promotional codes"""
        if PromoCode.objects.count() == 0:
            promo_codes = [
                {
                    'code': 'WELCOME10',
                    'description': 'Welcome offer - 10% off',
                    'discount_type': 'percentage',
                    'discount_value': Decimal('10.00'),
                    'min_amount': Decimal('200.00'),
                    'max_discount': Decimal('100.00'),
                    'usage_limit': 100,
                    'valid_from': timezone.now(),
                    'valid_until': timezone.now() + timedelta(days=30),
                },
                {
                    'code': 'MOVIE50',
                    'description': 'Flat ₹50 off on movie bookings',
                    'discount_type': 'fixed',
                    'discount_value': Decimal('50.00'),
                    'min_amount': Decimal('300.00'),
                    'usage_limit': 50,
                    'valid_from': timezone.now(),
                    'valid_until': timezone.now() + timedelta(days=15),
                }
            ]
            
            for promo_data in promo_codes:
                promo = PromoCode.objects.create(**promo_data)
                self.stdout.write(f'Created promo code: {promo.code}')
