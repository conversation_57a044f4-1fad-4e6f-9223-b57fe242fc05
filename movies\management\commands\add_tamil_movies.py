from django.core.management.base import BaseCommand
from movies.services import TMDBService
from movies.models import Movie
from datetime import datetime
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Add Tamil movies from TMDB API'

    def add_arguments(self, parser):
        parser.add_argument(
            '--pages',
            type=int,
            default=3,
            help='Number of pages to fetch from TMDB (default: 3)'
        )
        parser.add_argument(
            '--category',
            type=str,
            default='popular',
            choices=['popular', 'now_playing', 'upcoming', 'top_rated'],
            help='Category of Tamil movies to fetch (default: popular)'
        )

    def handle(self, *args, **options):
        pages = options['pages']
        category = options['category']
        
        self.stdout.write(f'Fetching Tamil movies from TMDB API ({category} category, {pages} pages)...')
        
        tmdb = TMDBService()
        
        # Check if API key is configured
        if not tmdb.api_key:
            self.stdout.write(
                self.style.ERROR('TMDB API key not configured. Please set TMDB_API_KEY in your .env file.')
            )
            return
        
        added_count = 0
        updated_count = 0
        
        try:
            for page in range(1, pages + 1):
                self.stdout.write(f'Fetching page {page}...')
                
                # Use discover endpoint to filter by Tamil language
                tamil_movies = tmdb.discover_movies(
                    with_original_language='ta',  # Tamil language code
                    sort_by='popularity.desc',
                    page=page,
                    vote_count_gte=10  # Minimum votes to ensure quality
                )
                
                if not tamil_movies or 'results' not in tamil_movies:
                    self.stdout.write(f'No results found for page {page}')
                    continue
                
                movies = tamil_movies['results']
                self.stdout.write(f'Found {len(movies)} Tamil movies on page {page}')
                
                for tmdb_movie in movies:
                    try:
                        # Get detailed movie information
                        movie_details = tmdb.get_movie_details(tmdb_movie['id'])
                        if not movie_details:
                            continue
                        
                        # Get cast and crew information
                        credits = tmdb.get_movie_credits(tmdb_movie['id'])
                        
                        # Format the movie data
                        movie_data = self._format_tamil_movie_data(movie_details, credits, tmdb)
                        
                        if not movie_data:
                            continue
                        
                        # Check if movie already exists
                        existing_movie = Movie.objects.filter(tmdb_id=movie_data['tmdb_id']).first()
                        
                        if existing_movie:
                            # Update existing movie
                            for key, value in movie_data.items():
                                if value:  # Only update non-empty values
                                    setattr(existing_movie, key, value)
                            existing_movie.save()
                            updated_count += 1
                            self.stdout.write(f'✓ Updated: {existing_movie.title}')
                        else:
                            # Create new movie
                            movie = Movie.objects.create(**movie_data)
                            added_count += 1
                            self.stdout.write(f'✓ Added: {movie.title} ({movie.release_date.year})')
                            
                    except Exception as e:
                        logger.error(f"Error processing movie {tmdb_movie.get('title', 'Unknown')}: {e}")
                        self.stdout.write(f'✗ Error processing: {tmdb_movie.get("title", "Unknown")}')
                        continue
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n🎬 Tamil movies sync completed!\n'
                    f'Added: {added_count} new movies\n'
                    f'Updated: {updated_count} existing movies'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error fetching Tamil movies: {e}')
            )
    
    def _format_tamil_movie_data(self, movie_details, credits, tmdb):
        """Format TMDB movie data specifically for Tamil movies"""
        try:
            # Extract cast information
            cast_list = []
            if credits and 'cast' in credits:
                cast_list = [actor['name'] for actor in credits['cast'][:10]]  # Top 10 actors
            
            # Extract director information
            director = 'Unknown'
            if credits and 'crew' in credits:
                for crew_member in credits['crew']:
                    if crew_member['job'] == 'Director':
                        director = crew_member['name']
                        break
            
            # Map TMDB genres to our genre choices
            genre_mapping = {
                28: 'action',
                35: 'comedy',
                18: 'drama',
                27: 'horror',
                10749: 'romance',
                53: 'thriller',
                878: 'sci-fi',
                14: 'fantasy',
                16: 'animation',
                99: 'documentary'
            }
            
            # Get primary genre
            primary_genre = 'drama'  # default
            if movie_details.get('genres'):
                for genre in movie_details['genres']:
                    if genre['id'] in genre_mapping:
                        primary_genre = genre_mapping[genre['id']]
                        break
            
            # Parse release date
            release_date = timezone.now().date()
            if movie_details.get('release_date'):
                try:
                    release_date = datetime.strptime(
                        movie_details['release_date'], '%Y-%m-%d'
                    ).date()
                except ValueError:
                    pass
            
            # Calculate rating (convert from 10-point to 5-point scale)
            rating = round(movie_details.get('vote_average', 0) / 2, 1)
            
            return {
                'title': movie_details.get('title', ''),
                'original_title': movie_details.get('original_title', ''),
                'description': movie_details.get('overview', ''),
                'genre': primary_genre,
                'language': 'tamil',
                'original_language': movie_details.get('original_language', 'ta'),
                'duration': movie_details.get('runtime', 120),
                'release_date': release_date,
                'cast': ', '.join(cast_list) if cast_list else '',
                'director': director,
                'rating': rating,
                'tmdb_id': movie_details.get('id'),
                'imdb_id': movie_details.get('imdb_id', ''),
                'poster_path': tmdb.get_full_image_url(movie_details.get('poster_path')),
                'backdrop_path': tmdb.get_full_image_url(movie_details.get('backdrop_path')),
                'popularity': movie_details.get('popularity', 0),
                'vote_average': movie_details.get('vote_average', 0),
                'vote_count': movie_details.get('vote_count', 0),
                'adult': movie_details.get('adult', False),
                'budget': movie_details.get('budget'),
                'revenue': movie_details.get('revenue'),
                'tagline': movie_details.get('tagline', ''),
                'homepage': movie_details.get('homepage', ''),
                'is_featured': movie_details.get('vote_average', 0) >= 7.0,  # Feature high-rated movies
                'is_now_showing': True,
                'is_coming_soon': False,
                'is_active': True,
            }
            
        except Exception as e:
            logger.error(f"Error formatting movie data: {e}")
            return None
