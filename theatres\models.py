from django.db import models
from django.urls import reverse
from movies.models import Movie

class Theatre(models.Model):
    name = models.CharField(max_length=200)
    address = models.TextField()
    city = models.CharField(max_length=100)
    phone = models.CharField(max_length=15)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} - {self.city}"

class Screen(models.Model):
    theatre = models.ForeignKey(Theatre, on_delete=models.CASCADE, related_name='screens')
    name = models.CharField(max_length=100)
    total_seats = models.PositiveIntegerField()
    rows = models.PositiveIntegerField(default=10)
    seats_per_row = models.PositiveIntegerField(default=10)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['theatre', 'name']

    def __str__(self):
        return f"{self.theatre.name} - {self.name}"

    def get_seat_layout(self):
        """Generate a simple seat layout"""
        layout = []
        for row in range(1, self.rows + 1):
            row_seats = []
            for seat in range(1, self.seats_per_row + 1):
                seat_number = f"{chr(64 + row)}{seat}"
                row_seats.append(seat_number)
            layout.append(row_seats)
        return layout

class ShowTime(models.Model):
    movie = models.ForeignKey(Movie, on_delete=models.CASCADE, related_name='showtimes')
    screen = models.ForeignKey(Screen, on_delete=models.CASCADE, related_name='showtimes')
    date = models.DateField()
    time = models.TimeField()
    price = models.DecimalField(max_digits=8, decimal_places=2)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['screen', 'date', 'time']
        ordering = ['date', 'time']

    def __str__(self):
        return f"{self.movie.title} - {self.screen} - {self.date} {self.time}"

    def get_absolute_url(self):
        return reverse('bookings:book_tickets', kwargs={'showtime_id': self.pk})
