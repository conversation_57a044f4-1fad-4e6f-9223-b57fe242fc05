from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.mail import send_mail
from django.conf import settings
from django.db import transaction
from theatres.models import ShowTime
from .models import Booking, BookedSeat
from decimal import Decimal

@login_required
def book_tickets(request, showtime_id):
    """Book tickets for a showtime"""
    showtime = get_object_or_404(ShowTime, pk=showtime_id, is_active=True)

    # Get booked seats for this showtime
    booked_seats = BookedSeat.objects.filter(showtime=showtime).values_list('seat_number', flat=True)

    # Generate seat layout
    seat_layout = showtime.screen.get_seat_layout()

    context = {
        'showtime': showtime,
        'booked_seats': list(booked_seats),
        'seat_layout': seat_layout,
    }
    return render(request, 'bookings/book_tickets.html', context)

@login_required
def confirm_booking(request):
    """Confirm and process booking"""
    if request.method == 'POST':
        showtime_id = request.POST.get('showtime_id')
        selected_seats = request.POST.getlist('seats')

        if not selected_seats:
            messages.error(request, 'Please select at least one seat.')
            return redirect('bookings:book_tickets', showtime_id=showtime_id)

        showtime = get_object_or_404(ShowTime, pk=showtime_id)

        # Check if seats are still available
        booked_seats = BookedSeat.objects.filter(
            showtime=showtime,
            seat_number__in=selected_seats
        ).values_list('seat_number', flat=True)

        if booked_seats:
            messages.error(request, f'Seats {", ".join(booked_seats)} are already booked.')
            return redirect('bookings:book_tickets', showtime_id=showtime_id)

        # Calculate total amount
        total_amount = Decimal(str(showtime.price)) * len(selected_seats)

        try:
            with transaction.atomic():
                # Create booking
                booking = Booking.objects.create(
                    user=request.user,
                    showtime=showtime,
                    seats=', '.join(selected_seats),
                    total_amount=total_amount
                )

                # Create booked seats
                for seat in selected_seats:
                    BookedSeat.objects.create(
                        showtime=showtime,
                        seat_number=seat,
                        booking=booking
                    )

                # Send confirmation email
                send_booking_confirmation_email(booking)

                messages.success(request, f'Booking confirmed! Booking ID: {booking.booking_id}')
                return redirect('bookings:booking_detail', booking_id=booking.booking_id)

        except Exception as e:
            messages.error(request, 'An error occurred while processing your booking. Please try again.')
            return redirect('bookings:book_tickets', showtime_id=showtime_id)

    return redirect('movies:home')

@login_required
def my_bookings(request):
    """User's booking history"""
    bookings = Booking.objects.filter(user=request.user).order_by('-booking_date')
    return render(request, 'bookings/my_bookings.html', {'bookings': bookings})

@login_required
def booking_detail(request, booking_id):
    """Booking detail view"""
    booking = get_object_or_404(Booking, booking_id=booking_id, user=request.user)
    return render(request, 'bookings/booking_detail.html', {'booking': booking})

@login_required
def cancel_booking(request, booking_id):
    """Cancel a booking"""
    booking = get_object_or_404(Booking, booking_id=booking_id, user=request.user)

    if booking.status == 'cancelled':
        messages.warning(request, 'This booking is already cancelled.')
        return redirect('bookings:booking_detail', booking_id=booking_id)

    if request.method == 'POST':
        booking.status = 'cancelled'
        booking.save()

        # Remove booked seats
        BookedSeat.objects.filter(booking=booking).delete()

        messages.success(request, 'Booking cancelled successfully.')
        return redirect('bookings:my_bookings')

    return render(request, 'bookings/cancel_booking.html', {'booking': booking})

def send_booking_confirmation_email(booking):
    """Send booking confirmation email"""
    subject = f'Booking Confirmation - {booking.booking_id}'
    message = f"""
    Dear {booking.user.get_full_name() or booking.user.username},

    Your booking has been confirmed!

    Booking Details:
    Booking ID: {booking.booking_id}
    Movie: {booking.showtime.movie.title}
    Theatre: {booking.showtime.screen.theatre.name}
    Screen: {booking.showtime.screen.name}
    Date: {booking.showtime.date}
    Time: {booking.showtime.time}
    Seats: {booking.seats}
    Total Amount: ₹{booking.total_amount}

    Thank you for choosing our service!
    """

    try:
        send_mail(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [booking.user.email],
            fail_silently=False,
        )
    except Exception:
        pass  # Email sending failed, but booking is still valid
